using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;
using System;
using System.Reflection;
using InventoryManagement.Services;
using InventoryManagement.Services.Implementations;
using InventoryManagement.Services.Interfaces;
using InventoryManagement.DataAccess;
using InventoryManagement.Repositories;
using InventoryManagement.Infrastructure.Auth;
using InventoryManagement.Infrastructure.Validation;
using InventoryManagement.Infrastructure.Exceptions;
using InventoryManagement.Utilities;
using InventoryManagement.Configuration;
using InventoryManagement.Services.Hardware;
using InventoryManagement.Services.Reports;
// Removed embedded PostgreSQL using statements since using system PostgreSQL
using InventoryManagement.Services.Export;
using InventoryManagement.ViewModels;
using InventoryManagement.ViewModels.Settings;
using InventoryManagement.ViewModels.Reports;
using InventoryManagement.DataAccess.Repositories;
using Microsoft.Extensions.Logging;
using Npgsql;
using Npgsql.EntityFrameworkCore.PostgreSQL;
using Polly;

namespace InventoryManagement
{
    public class Startup
    {
        public IConfiguration Configuration { get; }

        public Startup(IConfiguration configuration)
        {
            Configuration = configuration;
        }

        public void ConfigureServices(IServiceCollection services)
        {
            // Register secure connection provider for local PostgreSQL
            services.AddSingleton<ISecureConnectionStringProvider, SecureConnectionStringProvider>();
            
            // Note: Using system PostgreSQL instead of embedded
            // Embedded PostgreSQL services disabled since system PostgreSQL is available

            // Register database connection service
            services.AddSingleton<IDatabaseConnectionService, DatabaseConnectionService>();

            // Add database health check
            services.AddSingleton<IDatabaseHealthCheck, DatabaseHealthCheck>();
            
            // Configure database context for system PostgreSQL
            services.AddDbContext<ApplicationDbContext>((serviceProvider, options) =>
            {
                var connectionProvider = serviceProvider.GetRequiredService<ISecureConnectionStringProvider>();
                var connectionString = connectionProvider.GetSecureConnectionString();
                
                options.UseNpgsql(connectionString, npgsqlOptions => 
                {
                    // Configure PostgreSQL for local network
                    npgsqlOptions.EnableRetryOnFailure(
                        maxRetryCount: 3,
                        maxRetryDelay: TimeSpan.FromSeconds(10),
                        retryBackoffType: Npgsql.Util.BackoffType.Linear);
                    
                    // Performance settings for local network
                    npgsqlOptions.MaxBatchSize(100);
                    npgsqlOptions.CommandTimeout(30);
                    
                    // Migration history table
                    npgsqlOptions.MigrationsHistoryTable("__EFMigrationsHistory", "migration");
                });
                
                // Add command timeout for queries
                options.EnableDetailedErrors(false); // Only enable in dev environment
                options.EnableSensitiveDataLogging(false); // Only enable in dev environment
            });
        
            // Register essential services for offline operation
            services.AddScoped<IInventoryService, InventoryService>();
            services.AddScoped<IUserService, UserServiceImpl>();
            
            // Register the offline notification service
            services.AddScoped<INotificationService, OfflineNotificationService>();
            
            // Register offline support services
            services.AddSingleton<ITerminalIdentityService, TerminalIdentityService>();
            services.AddScoped<IOfflineQueueService, OfflineQueueService>();
            services.AddScoped<IConflictResolutionService, ConflictResolutionService>();
            
            // Configure offline sync options
            services.Configure<OfflineSyncOptions>(Configuration.GetSection("OfflineSync"));
            
            // Register repositories
            services.AddScoped(typeof(IGenericRepository<,>), typeof(GenericRepository<,>));
            services.AddScoped<IItemRepository, ItemRepository>();
            
            // Register validation 
            services.AddScoped<IModelValidator, ModelValidator>();
            
            // Register security services
            services.AddSingleton<IConcurrencyManager, ConcurrencyManager>();
            services.AddSingleton<ILoginRateLimitService, LoginRateLimitService>();
            
            // Register error handling services
            services.AddSingleton<IErrorHandlingService, ErrorHandlingService>();
            services.AddSingleton<IExceptionHandler, EnhancedExceptionHandler>();
            
            // Register database migration and backup service
            services.AddScoped<IDatabaseMigrationService, DatabaseMigrationService>();
            services.AddScoped<IDatabaseBackupService, LocalDatabaseBackupService>();
            services.AddScoped<IScheduledBackupService, ScheduledBackupService>();
            
            // Register credit and financial services
            services.AddScoped<ICreditService, CreditService>();
            services.AddScoped<IFinancialService, FinancialService>();

            // Register customer management services
            services.AddScoped<ICustomerRepository, CustomerRepository>();
            services.AddScoped<ICustomerService, CustomerService>();

            // Register hardware services
            services.AddScoped<IBarcodeHardwareService, BarcodeHardwareService>();
            services.AddScoped<IPrinterHardwareService, PrinterHardwareService>();

            // Register return and discount services
            services.AddScoped<IReturnService, ReturnService>();
            services.AddScoped<IDiscountService, DiscountService>();

            // Register reporting services
            services.AddScoped<ISalesReportService, SalesReportService>();
            services.AddScoped<IInventoryReportService, InventoryReportService>();
            services.AddScoped<IFinancialReportService, FinancialReportService>();

            // Register export service
            services.AddScoped<IExportService, ExportService>();

            // Register ViewModels
            services.AddTransient<CustomerManagementViewModel>();
            services.AddTransient<HardwareSettingsViewModel>();
            services.AddTransient<ReportsDashboardViewModel>();
            
            // Register connection string encryption tool
            services.AddTransient<ConnectionStringEncryptionTool>();
            
            // Authorization - using simplified approach for offline desktop app
            services.AddScoped<IAuthorizationHandler, PermissionAuthorizationHandler>();
            
            // Register Dashboard Service (offline compatible)
            services.AddScoped<IDashboardService, DashboardService>();
            
            // Register Audit Service
            services.AddScoped<IAuditService, AuditService>();
            
            // Register Cache Service
            services.AddScoped<ICacheService, CacheService>();
            
            // Register User Preferences Service
            services.AddScoped<IUserPreferencesService, UserPreferencesService>();
            
            // Register Synchronization Services
            services.AddScoped<ISyncService, SyncService>();
            services.AddScoped<IOfflineOperationService, OfflineOperationService>();
            services.AddScoped<ISyncHistoryService, SyncHistoryService>();
            
            // Register Network Status Service as a singleton to monitor connectivity
            services.AddSingleton<INetworkStatusService, NetworkStatusService>();
            
            // Register App Settings Service for synchronization configuration
            services.AddScoped<IAppSettingsService, AppSettingsService>();
            
            // Register Auto Sync Background Service
            services.AddHostedService<AutoSyncService>();
        }
        
        // Configure method for WPF application initialization
        public void Configure()
        {
            // This method would contain application startup logic for a WPF app
            // such as initializing the database, setting up error handlers, etc.
            
            // For a WPF app, we don't need the web-specific middleware configuration
            // that would be in a typical ASP.NET Core Configure method
        }
    }
} 