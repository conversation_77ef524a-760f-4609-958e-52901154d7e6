using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using Microsoft.EntityFrameworkCore;
using Microsoft.Win32;
using InventoryManagement.Commands;
using InventoryManagement.DataAccess;
using InventoryManagement.Models;
using InventoryManagement.Services;
using InventoryManagement.ViewModels.Shared;

namespace InventoryManagement.ViewModels
{
    /// <summary>
    /// ViewModel for the Transfer Dialog, handling inventory transfers between locations
    /// </summary>
    public class TransferDialogViewModel : ViewModelBase
    {
        private readonly ApplicationDbContext _dbContext;
        private readonly IAuditService _auditService;
        private readonly INotificationService _notificationService;
        private readonly User _currentUser;
        private readonly EnhancedExceptionHandler _exceptionHandler;
        
        private readonly InventoryTransfer _transfer;
        
        private string _windowTitle;
        private bool _isNewTransfer;
        private ObservableCollection<ItemViewModel> _availableItems;
        private ItemViewModel _selectedItem;
        private ObservableCollection<Location> _sourceLocations;
        private Location _selectedSourceLocation;
        private ObservableCollection<Location> _destinationLocations;
        private Location _selectedDestinationLocation;
        private decimal _quantity;
        private string _searchText;
        private DateTime _transferDate;
        private ObservableCollection<string> _statusOptions;
        private string _selectedStatus;
        private ObservableCollection<string> _purposeOptions;
        private string _selectedPurpose;
        private ObservableCollection<string> _priorityOptions;
        private string _selectedPriority;
        private string _notes;
        private bool _sendNotification = true;
        private string _attachmentPath;
        private bool _isBusy;
        
        public TransferDialogViewModel(
            InventoryTransfer transfer,
            bool isNewTransfer,
            ApplicationDbContext dbContext,
            IAuditService auditService,
            INotificationService notificationService,
            IUserService userService,
            EnhancedExceptionHandler exceptionHandler)
        {
            _transfer = transfer ?? new InventoryTransfer();
            _isNewTransfer = isNewTransfer;
            _dbContext = dbContext ?? throw new ArgumentNullException(nameof(dbContext));
            _auditService = auditService ?? throw new ArgumentNullException(nameof(auditService));
            _notificationService = notificationService ?? throw new ArgumentNullException(nameof(notificationService));
            _currentUser = userService?.GetCurrentUser() ?? throw new ArgumentNullException(nameof(userService));
            _exceptionHandler = exceptionHandler ?? throw new ArgumentNullException(nameof(exceptionHandler));
            
            WindowTitle = isNewTransfer ? "Create New Transfer" : "Edit Transfer";
            
            // Initialize collections
            AvailableItems = new ObservableCollection<ItemViewModel>();
            SourceLocations = new ObservableCollection<Location>();
            DestinationLocations = new ObservableCollection<Location>();
            StatusOptions = new ObservableCollection<string>(new[] { "Pending", "In Progress", "Completed", "Cancelled" });
            PurposeOptions = new ObservableCollection<string>(new[] { "For Sale", "For Display", "Restocking", "Inspection", "Return", "Other" });
            PriorityOptions = new ObservableCollection<string>(new[] { "Low", "Normal", "High", "Urgent" });
            
            // Initialize commands
            SearchCommand = new RelayCommand(_ => SearchItems());
            BrowseCommand = new RelayCommand(_ => BrowseAttachment());
            SaveCommand = new RelayCommand(_ => Save(), _ => CanSave());
            CancelCommand = new RelayCommand(_ => Cancel());
            
            // Set default values for new transfers
            if (isNewTransfer)
            {
                TransferDate = DateTime.Now;
                SelectedStatus = "Pending";
                SelectedPriority = "Normal";
                SelectedPurpose = "For Sale";
                _transfer.TransactionDate = DateTime.Now;
                _transfer.InitiatedByUserId = _currentUser.Id;
                _transfer.InitiatedByUserName = _currentUser.FullName;
                
                // Default quantity to 1
                Quantity = 1;
            }
            else
            {
                // Load existing transfer values
                LoadExistingTransfer();
            }
            
            // Load data
            LoadDataAsync();
        }
        
        #region Properties
        
        public string WindowTitle
        {
            get => _windowTitle;
            set => SetProperty(ref _windowTitle, value);
        }
        
        public ObservableCollection<ItemViewModel> AvailableItems
        {
            get => _availableItems;
            set => SetProperty(ref _availableItems, value);
        }
        
        public ItemViewModel SelectedItem
        {
            get => _selectedItem;
            set
            {
                if (SetProperty(ref _selectedItem, value) && value != null)
                {
                    _transfer.ItemId = value.Id;
                    _transfer.ItemName = value.Name;
                    _transfer.Barcode = value.Barcode;
                    _transfer.Category = value.Category;
                    
                    // Update available quantity based on selected item and location
                    UpdateAvailableQuantity();
                    
                    OnPropertyChanged(nameof(TransferSummary));
                }
            }
        }
        
        public ObservableCollection<Location> SourceLocations
        {
            get => _sourceLocations;
            set => SetProperty(ref _sourceLocations, value);
        }
        
        public Location SelectedSourceLocation
        {
            get => _selectedSourceLocation;
            set
            {
                if (SetProperty(ref _selectedSourceLocation, value) && value != null)
                {
                    _transfer.SourceLocationId = value.Id;
                    _transfer.SourceLocationName = value.Name;
                    
                    // Update destination locations (exclude source)
                    UpdateDestinationLocations();
                    
                    // Reload items for this location
                    LoadItemsForLocation(value.Id);
                    
                    OnPropertyChanged(nameof(TransferSummary));
                }
            }
        }
        
        public ObservableCollection<Location> DestinationLocations
        {
            get => _destinationLocations;
            set => SetProperty(ref _destinationLocations, value);
        }
        
        public Location SelectedDestinationLocation
        {
            get => _selectedDestinationLocation;
            set
            {
                if (SetProperty(ref _selectedDestinationLocation, value) && value != null)
                {
                    _transfer.DestinationLocationId = value.Id;
                    _transfer.DestinationLocationName = value.Name;
                    
                    OnPropertyChanged(nameof(TransferSummary));
                }
            }
        }
        
        public decimal Quantity
        {
            get => _quantity;
            set
            {
                if (SetProperty(ref _quantity, value))
                {
                    _transfer.Quantity = value;
                    OnPropertyChanged(nameof(TransferSummary));
                }
            }
        }
        
        public string SearchText
        {
            get => _searchText;
            set => SetProperty(ref _searchText, value);
        }
        
        public DateTime TransferDate
        {
            get => _transferDate;
            set
            {
                if (SetProperty(ref _transferDate, value))
                {
                    _transfer.TransactionDate = value;
                    OnPropertyChanged(nameof(TransferSummary));
                }
            }
        }
        
        public ObservableCollection<string> StatusOptions
        {
            get => _statusOptions;
            set => SetProperty(ref _statusOptions, value);
        }
        
        public string SelectedStatus
        {
            get => _selectedStatus;
            set
            {
                if (SetProperty(ref _selectedStatus, value))
                {
                    _transfer.Status = value;
                    OnPropertyChanged(nameof(TransferSummary));
                }
            }
        }
        
        public ObservableCollection<string> PurposeOptions
        {
            get => _purposeOptions;
            set => SetProperty(ref _purposeOptions, value);
        }
        
        public string SelectedPurpose
        {
            get => _selectedPurpose;
            set
            {
                if (SetProperty(ref _selectedPurpose, value))
                {
                    // Include purpose in the transfer reason
                    if (!string.IsNullOrEmpty(value))
                    {
                        string notes = _notes ?? string.Empty;
                        if (!notes.Contains($"Purpose: {value}"))
                        {
                            _transfer.Reason = $"Purpose: {value}";
                        }
                    }
                    
                    OnPropertyChanged(nameof(TransferSummary));
                }
            }
        }
        
        public ObservableCollection<string> PriorityOptions
        {
            get => _priorityOptions;
            set => SetProperty(ref _priorityOptions, value);
        }
        
        public string SelectedPriority
        {
            get => _selectedPriority;
            set => SetProperty(ref _selectedPriority, value);
        }
        
        public string Notes
        {
            get => _notes;
            set
            {
                if (SetProperty(ref _notes, value))
                {
                    _transfer.Notes = value;
                    OnPropertyChanged(nameof(TransferSummary));
                }
            }
        }
        
        public bool SendNotification
        {
            get => _sendNotification;
            set => SetProperty(ref _sendNotification, value);
        }
        
        public string AttachmentPath
        {
            get => _attachmentPath;
            set => SetProperty(ref _attachmentPath, value);
        }
        
        public bool IsBusy
        {
            get => _isBusy;
            set => SetProperty(ref _isBusy, value);
        }
        
        public decimal MaxAvailableQuantity
        {
            get
            {
                if (SelectedItem == null || SelectedSourceLocation == null)
                    return 0;
                
                return SelectedItem.AvailableQuantity;
            }
        }
        
        public string QuantityUnit => "Units"; // Could be customized based on item type
        
        public string TransferSummary
        {
            get
            {
                var summary = new System.Text.StringBuilder();
                
                if (SelectedItem != null && SelectedSourceLocation != null && SelectedDestinationLocation != null)
                {
                    summary.AppendLine($"Transfer {Quantity} {QuantityUnit} of {SelectedItem.Name} ({SelectedItem.SKU})");
                    summary.AppendLine($"From: {SelectedSourceLocation.Name}");
                    summary.AppendLine($"To: {SelectedDestinationLocation.Name}");
                    summary.AppendLine($"Date: {TransferDate:MM/dd/yyyy}");
                    summary.AppendLine($"Status: {SelectedStatus}");
                    
                    if (!string.IsNullOrEmpty(SelectedPurpose))
                    {
                        summary.AppendLine($"Purpose: {SelectedPurpose}");
                    }
                    
                    if (!string.IsNullOrEmpty(Notes))
                    {
                        summary.AppendLine($"Notes: {Notes}");
                    }
                }
                
                return summary.ToString();
            }
        }
        
        public bool IsNewTransfer => _isNewTransfer;
        
        #endregion
        
        #region Commands
        
        public ICommand SearchCommand { get; }
        public ICommand BrowseCommand { get; }
        public ICommand SaveCommand { get; }
        public ICommand CancelCommand { get; }
        
        #endregion
        
        #region Methods
        
        private async void LoadDataAsync()
        {
            try
            {
                IsBusy = true;
                
                // Load locations
                await LoadLocationsAsync();
                
                // If editing an existing transfer, select the appropriate locations
                if (!_isNewTransfer && _transfer.Id > 0)
                {
                    SelectedSourceLocation = SourceLocations.FirstOrDefault(l => l.Id == _transfer.SourceLocationId);
                    SelectedDestinationLocation = DestinationLocations.FirstOrDefault(l => l.Id == _transfer.DestinationLocationId);
                }
                else if (SourceLocations.Count > 0)
                {
                    // For new transfers, select the first source location by default
                    SelectedSourceLocation = SourceLocations.FirstOrDefault();
                }
            }
            catch (Exception ex)
            {
                await _exceptionHandler.HandleExceptionAsync(ex, "Error loading transfer data");
            }
            finally
            {
                IsBusy = false;
            }
        }
        
        private async Task LoadLocationsAsync()
        {
            try
            {
                // Get all active locations
                var locations = await _dbContext.Locations
                    .Where(l => l.IsActive)
                    .OrderBy(l => l.Name)
                    .ToListAsync();
                
                // Update source locations collection
                SourceLocations.Clear();
                foreach (var location in locations)
                {
                    SourceLocations.Add(location);
                }
                
                // Update destination locations (excluding selected source)
                UpdateDestinationLocations();
            }
            catch (Exception ex)
            {
                await _exceptionHandler.HandleExceptionAsync(ex, "Error loading locations");
            }
        }
        
        private void UpdateDestinationLocations()
        {
            DestinationLocations.Clear();
            
            if (SelectedSourceLocation == null)
                return;
            
            // Add all locations except the selected source
            foreach (var location in SourceLocations.Where(l => l.Id != SelectedSourceLocation.Id))
            {
                DestinationLocations.Add(location);
            }
            
            // Select the first destination by default if none is selected
            if (SelectedDestinationLocation == null && DestinationLocations.Count > 0)
            {
                SelectedDestinationLocation = DestinationLocations.FirstOrDefault();
            }
        }
        
        private async void LoadItemsForLocation(int locationId)
        {
            try
            {
                IsBusy = true;
                
                // Get items with stock in the selected location
                var itemsWithStock = await _dbContext.ItemStocks
                    .Where(s => s.LocationId == locationId && s.Quantity > 0)
                    .Join(_dbContext.Items,
                        stock => stock.ItemId,
                        item => item.Id,
                        (stock, item) => new
                        {
                            Item = item,
                            AvailableQuantity = stock.Quantity
                        })
                    .Where(x => !x.Item.IsDeleted && x.Item.IsActive)
                    .OrderBy(x => x.Item.Name)
                    .ToListAsync();
                
                // Update available items collection
                AvailableItems.Clear();
                foreach (var itemStock in itemsWithStock)
                {
                    var itemViewModel = new ItemViewModel
                    {
                        Id = itemStock.Item.Id,
                        Name = itemStock.Item.Name,
                        SKU = itemStock.Item.SKU,
                        Barcode = itemStock.Item.Barcode,
                        Category = itemStock.Item.Category,
                        AvailableQuantity = itemStock.AvailableQuantity
                    };
                    
                    AvailableItems.Add(itemViewModel);
                }
                
                // If editing an existing transfer, select the appropriate item
                if (!_isNewTransfer && _transfer.ItemId > 0)
                {
                    SelectedItem = AvailableItems.FirstOrDefault(i => i.Id == _transfer.ItemId);
                }
                else if (AvailableItems.Count > 0)
                {
                    // For new transfers, select the first item by default
                    SelectedItem = AvailableItems.FirstOrDefault();
                }
            }
            catch (Exception ex)
            {
                await _exceptionHandler.HandleExceptionAsync(ex, "Error loading items for location");
            }
            finally
            {
                IsBusy = false;
            }
        }
        
        private void LoadExistingTransfer()
        {
            // Set transfer properties from existing transfer
            TransferDate = _transfer.TransactionDate;
            SelectedStatus = _transfer.Status;
            Quantity = _transfer.Quantity;
            Notes = _transfer.Notes;
            
            // Extract purpose from reason if available
            if (!string.IsNullOrEmpty(_transfer.Reason) && _transfer.Reason.StartsWith("Purpose: "))
            {
                string purpose = _transfer.Reason.Substring("Purpose: ".Length);
                SelectedPurpose = PurposeOptions.Contains(purpose) ? purpose : "Other";
            }
        }
        
        private void SearchItems()
        {
            if (string.IsNullOrWhiteSpace(SearchText) || SelectedSourceLocation == null)
                return;
            
            // Filter available items based on search text
            var filteredItems = AvailableItems.Where(i => 
                i.Name.Contains(SearchText, StringComparison.OrdinalIgnoreCase) ||
                i.SKU.Contains(SearchText, StringComparison.OrdinalIgnoreCase) ||
                i.Barcode.Contains(SearchText, StringComparison.OrdinalIgnoreCase) ||
                i.Category.Contains(SearchText, StringComparison.OrdinalIgnoreCase)
            ).ToList();
            
            // If we have exactly one match, select it
            if (filteredItems.Count == 1)
            {
                SelectedItem = filteredItems[0];
            }
        }
        
        private void BrowseAttachment()
        {
            var openFileDialog = new OpenFileDialog
            {
                Filter = "All Files (*.*)|*.*",
                Title = "Select Attachment"
            };
            
            if (openFileDialog.ShowDialog() == true)
            {
                AttachmentPath = openFileDialog.FileName;
            }
        }
        
        private void UpdateAvailableQuantity()
        {
            if (SelectedItem == null || SelectedSourceLocation == null)
                return;
            
            OnPropertyChanged(nameof(MaxAvailableQuantity));
        }
        
        private bool CanSave()
        {
            // Basic validation
            if (SelectedItem == null || SelectedSourceLocation == null || SelectedDestinationLocation == null)
                return false;
            
            if (Quantity <= 0 || Quantity > MaxAvailableQuantity)
                return false;
            
            return true;
        }
        
        public void Cancel()
        {
            // Set dialog result
            DialogResult = false;
        }
        
        public bool? DialogResult { get; set; }
        
        public async void Save()
        {
            try
            {
                // Validate
                if (!CanSave())
                    return;
                
                IsBusy = true;
                
                // Update transfer data
                _transfer.TransactionDate = TransferDate;
                _transfer.Status = SelectedStatus;
                _transfer.Quantity = Quantity;
                _transfer.Notes = Notes;
                
                // Save to database
                if (_isNewTransfer)
                {
                    // Generate transfer number
                    _transfer.Status = "Pending";
                    await _dbContext.InventoryTransfers.AddAsync(_transfer);
                }
                else
                {
                    _dbContext.InventoryTransfers.Update(_transfer);
                }
                
                await _dbContext.SaveChangesAsync();
                
                // Log audit trail
                string action = _isNewTransfer ? "Create" : "Update";
                await _auditService.LogActionAsync(
                    "InventoryTransfer",
                    action,
                    $"{action}d transfer of {Quantity} {SelectedItem.Name} from {SelectedSourceLocation.Name} to {SelectedDestinationLocation.Name}",
                    _transfer.Id.ToString());
                
                // Send notification if requested
                if (SendNotification)
                {
                    await SendTransferNotificationAsync();
                }
                
                // Set dialog result
                DialogResult = true;
            }
            catch (Exception ex)
            {
                await _exceptionHandler.HandleExceptionAsync(ex, "Error saving transfer");
                DialogResult = false;
            }
            finally
            {
                IsBusy = false;
            }
        }
        
        private async Task SendTransferNotificationAsync()
        {
            try
            {
                // Get users responsible for the destination location
                var destinationUsers = await _dbContext.Users
                    .Where(u => u.PrimaryLocationId == SelectedDestinationLocation.Id && u.IsActive)
                    .ToListAsync();
                
                if (destinationUsers.Any())
                {
                    // Create notification for each user
                    foreach (var user in destinationUsers)
                    {
                        var notification = new Notification
                        {
                            Title = "New Inventory Transfer",
                            Message = $"Transfer of {Quantity} {SelectedItem.Name} from {SelectedSourceLocation.Name} to {SelectedDestinationLocation.Name}. Purpose: {SelectedPurpose}",
                            Type = NotificationType.InventoryTransfer,
                            UserId = user.Id,
                            SourceUserId = _currentUser.Id,
                            CreatedDate = DateTime.Now,
                            Priority = NotificationPriority.Normal,
                            IsRead = false,
                            RelatedEntityId = _transfer.Id.ToString()
                        };
                        
                        await _notificationService.SendNotificationAsync(notification);
                    }
                }
            }
            catch (Exception ex)
            {
                await _exceptionHandler.HandleExceptionAsync(ex, "Error sending transfer notification");
            }
        }
        
        /// <summary>
        /// Returns the inventory transfer object with all updated values
        /// </summary>
        /// <returns>The updated inventory transfer</returns>
        public InventoryTransfer GetTransfer()
        {
            return _transfer;
        }
        
        #endregion
    }
}
