<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net8.0-windows</TargetFramework>
    <UseWPF>true</UseWPF>
    <Nullable>enable</Nullable>
    <NoWarn>CS8600;CS8601;CS8602;CS8603;CS8604;CS8618;CS8625;CS0108;CS8767;CS8612</NoWarn>
    <ImplicitUsings>enable</ImplicitUsings>
    <EnableDefaultCompileItems>false</EnableDefaultCompileItems>
    <EnableDefaultPageItems>false</EnableDefaultPageItems>
    <EnableDefaultEmbeddedResourceItems>false</EnableDefaultEmbeddedResourceItems>
    <StartupObject>InventoryManagement.App</StartupObject>
  </PropertyGroup>

  <ItemGroup>
    <!-- Essential Core Packages -->
    <PackageReference Include="BCrypt.Net-Next" Version="4.0.3" />
    <PackageReference Include="FluentValidation" Version="12.0.0" />
    <PackageReference Include="FluentValidation.DependencyInjectionExtensions" Version="12.0.0" />
    
    <!-- Database - PostgreSQL -->
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="8.0.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="8.0.0">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Logging" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Logging.Console" Version="8.0.0" />
    
    <!-- Configuration -->
    <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Hosting" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Caching.Memory" Version="9.0.0" />
    <PackageReference Include="Microsoft.Extensions.Localization" Version="8.0.0" />
    <PackageReference Include="Polly" Version="8.2.0" />
    <PackageReference Include="Serilog.Extensions.Logging.File" Version="3.0.0" />
    <PackageReference Include="System.CommandLine" Version="2.0.0-beta4.22272.1" />
    <PackageReference Include="System.Management" Version="9.0.5" />
    
    <!-- Barcode Generation (Offline Compatible) -->
    <PackageReference Include="ZXing.Net" Version="0.16.9" />
    <PackageReference Include="System.Drawing.Common" Version="8.0.0" />
    
    <!-- PDF Report Generation (Offline Compatible) -->
    <PackageReference Include="QuestPDF" Version="2023.12.5" />
    
    <!-- Excel Generation (Offline Compatible) -->
    <PackageReference Include="EPPlus" Version="6.2.10" />
    
    <!-- UI Component Library (Offline Compatible) -->
    <PackageReference Include="MaterialDesignThemes" Version="4.9.0" />
    <PackageReference Include="MaterialDesignColors" Version="2.1.4" />

    <!-- Additional required packages -->
    <PackageReference Include="System.IO.Ports" Version="8.0.0" />
    <PackageReference Include="Microsoft.Xaml.Behaviors.Wpf" Version="1.1.39" />
  </ItemGroup>

  <ItemGroup>
    <Compile Include="Program.cs" />
    <Compile Include="Converters\**\*.cs" />
    <Compile Include="OfflineApp.cs" />
    <Compile Include="Startup.cs" />
    
    <!-- Main application files -->
    <Compile Include="App.xaml.cs" DependentUpon="App.xaml" />
    <Page Include="App.xaml" />
    
    <!-- Include all XAML views -->
    <Compile Include="Views\**\*.cs" />
    <Page Include="Views\**\*.xaml" />
    
    <!-- Include controls -->
    <Compile Include="Controls\**\*.cs" />
    <Page Include="Controls\**\*.xaml" />
    
    <!-- Include dialogs -->
    <Compile Include="Dialogs\**\*.cs" />
    <Page Include="Dialogs\**\*.xaml" />
    
    <!-- Include models -->
    <Compile Include="Models\**\*.cs" />
    
    <!-- Include services -->
    <Compile Include="Services\**\*.cs" />
    
    <!-- Include view models -->
    <Compile Include="ViewModels\**\*.cs" />
    
    <!-- Include infrastructure -->
    <Compile Include="Infrastructure\**\*.cs" />
    
    <!-- Include other important folders -->
    <Compile Include="Extensions\**\*.cs" />
    <Compile Include="Helpers\**\*.cs" />
    <Compile Include="Events\**\*.cs" />
    <Compile Include="BusinessRules\**\*.cs" />
    <Compile Include="Config\**\*.cs" />
    <Compile Include="Configuration\**\*.cs" />
    <Compile Include="DataAccess\**\*.cs" />
    <Compile Include="Database\**\*.cs" />
    <Compile Include="Domain\**\*.cs" />
    <Compile Include="Jobs\**\*.cs" />
    <Compile Include="Repositories\**\*.cs" />
    <Compile Include="Tools\**\*.cs" />
    <Compile Include="Utilities\**\*.cs" />
    
    <!-- Include resource dictionaries -->
    <Page Include="Resources\**\*.xaml" />
    <Page Include="Themes\**\*.xaml" />
    
    <!-- Include resources and assets -->
    <None Include="appsettings.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    
    <!-- Include resource files for localization -->
    <EmbeddedResource Include="Resources\*.resx" />
  </ItemGroup>

</Project>









