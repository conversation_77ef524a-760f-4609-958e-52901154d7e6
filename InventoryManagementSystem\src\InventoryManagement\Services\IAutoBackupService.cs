using System;
using System.Threading.Tasks;

namespace InventoryManagement.Services
{
    /// <summary>
    /// Interface for automatic backup service
    /// </summary>
    public interface IAutoBackupService
    {
        /// <summary>
        /// Event raised when a backup is completed
        /// </summary>
        event EventHandler<BackupCompletedEventArgs> BackupCompleted;
        
        /// <summary>
        /// Starts the auto backup service
        /// </summary>
        void Start();
        
        /// <summary>
        /// Stops the auto backup service
        /// </summary>
        void Stop();
        
        /// <summary>
        /// Performs a backup immediately
        /// </summary>
        /// <returns>True if successful</returns>
        Task<bool> PerformBackupAsync();
    }
    

}
