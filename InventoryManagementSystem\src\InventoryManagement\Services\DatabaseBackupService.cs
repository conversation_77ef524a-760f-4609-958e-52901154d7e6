using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using System.Linq;
using InventoryManagement.Models;
using System.Diagnostics;
using System.Globalization;
using Npgsql;
using System.Text.RegularExpressions;

namespace InventoryManagement.Services
{
    /// <summary>
    /// Service for database backup and restore operations
    /// </summary>
    public class DatabaseBackupService : IDatabaseBackupService
    {
        private readonly string _connectionString;
        private readonly string _backupPath;
        private readonly IErrorHandlingService _errorHandlingService;
        private readonly IAuditService _auditService;
        private readonly IBackupVerificationService _backupVerificationService;
        
        public DatabaseBackupService(
            string connectionString, 
            string backupPath,
            IErrorHandlingService errorHandlingService,
            IAuditService auditService,
            IBackupVerificationService backupVerificationService)
        {
            _connectionString = connectionString ?? throw new ArgumentNullException(nameof(connectionString));
            _backupPath = backupPath ?? throw new ArgumentNullException(nameof(backupPath));
            _errorHandlingService = errorHandlingService ?? throw new ArgumentNullException(nameof(errorHandlingService));
            _auditService = auditService ?? throw new ArgumentNullException(nameof(auditService));
            _backupVerificationService = backupVerificationService ?? throw new ArgumentNullException(nameof(backupVerificationService));
            
            // Ensure backup directory exists
            if (!Directory.Exists(_backupPath))
            {
                Directory.CreateDirectory(_backupPath);
            }
        }
        
        /// <summary>
        /// Creates a database backup
        /// </summary>
        /// <param name="userId">ID of the user performing the backup</param>
        /// <param name="full">Whether to create a full backup (true) or differential backup (false)</param>
        /// <returns>Information about the created backup, or null if failed</returns>
        public async Task<BackupInfo> CreateBackupAsync(int userId, bool full = true)
        {
            try
            {
                var backupType = full ? BackupType.Full : BackupType.Differential;
                var timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
                var filename = $"{backupType}_{timestamp}.backup";
                var backupFile = Path.Combine(_backupPath, filename);
                
                // Extract database info from connection string
                var builder = new NpgsqlConnectionStringBuilder(_connectionString);
                var databaseName = builder.Database;
                var host = builder.Host;
                var port = builder.Port;
                var username = builder.Username;
                var password = builder.Password;
                
                // Create pg_dump command with appropriate flags
                // -F c: Custom format (compressed, most flexible)
                // -b: Include large objects
                // -v: Verbose output
                // -Z 9: Maximum compression level
                string backupCommand = $"pg_dump -h {host} -p {port} -U {username} -F c -b -v -Z 9 -f \"{backupFile}\" {databaseName}";
                
                // Execute pg_dump command
                var process = new Process
                {
                    StartInfo = new ProcessStartInfo
                    {
                        FileName = "cmd.exe",
                        Arguments = $"/c {backupCommand}",
                        RedirectStandardOutput = true,
                        RedirectStandardError = true,
                        RedirectStandardInput = true,
                        UseShellExecute = false,
                        CreateNoWindow = true
                    }
                };
                
                process.Start();
                
                // Provide password if needed
                process.StandardInput.WriteLine(password);
                process.StandardInput.Close();
                
                // Wait for process to complete
                await process.WaitForExitAsync();
                
                if (process.ExitCode == 0)
                {
                    // Backup created successfully, now verify it
                    var verificationResult = await _backupVerificationService.VerifyBackupQuickAsync(backupFile);
                    
                    if (!verificationResult.Passed)
                    {
                        // Backup verification failed
                        await _errorHandlingService.LogErrorAsync("DatabaseBackupService.CreateBackupAsync", 
                            "Backup verification failed", string.Join("\n", verificationResult.Details));
                            
                        // Delete the failed backup
                        try { File.Delete(backupFile); } catch { /* Ignore deletion errors */ }
                        
                        return null;
                    }
                    
                    // Backup successful and verified
                    var fileInfo = new FileInfo(backupFile);
                    var backupInfo = new BackupInfo
                    {
                        Id = Guid.NewGuid().ToString("N"),
                        FileName = filename,
                        FilePath = backupFile,
                        CreatedAt = DateTime.Now,
                        CreatedBy = userId,
                        BackupType = backupType,
                        FileSize = fileInfo.Length,
                        Description = $"{backupType} backup created on {DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")}"
                    };
                    
                    // Log backup creation
                    await _auditService.LogActivityAsync(userId, "Database", "Backup", 
                        $"Created {backupType} database backup: {filename}");
                    
                    // Store backup metadata in database
                    await SaveBackupMetadataAsync(backupInfo);
                    
                    return backupInfo;
                }
                else
                {
                    // Backup failed
                    string error = await process.StandardError.ReadToEndAsync();
                    await _errorHandlingService.LogErrorAsync("DatabaseBackupService.CreateBackupAsync", 
                        $"Backup failed with exit code {process.ExitCode}", error);
                    
                    return null;
                }
            }
            catch (Exception ex)
            {
                await _errorHandlingService.LogErrorAsync("DatabaseBackupService.CreateBackupAsync", 
                    ex.Message, ex.StackTrace);
                return null;
            }
        }
        
        /// <summary>
        /// Restores a database from a backup file
        /// </summary>
        /// <param name="userId">ID of the user performing the restore</param>
        /// <param name="backupFilePath">Path to the backup file</param>
        /// <returns>True if successful, otherwise false</returns>
        public async Task<bool> RestoreFromBackupAsync(int userId, string backupFilePath)
        {
            try
            {
                if (!File.Exists(backupFilePath))
                {
                    await _errorHandlingService.LogErrorAsync("DatabaseBackupService.RestoreFromBackupAsync", 
                        "Backup file not found", backupFilePath);
                    return false;
                }
                
                // Extract database info from connection string
                var builder = new NpgsqlConnectionStringBuilder(_connectionString);
                var databaseName = builder.Database;
                var host = builder.Host;
                var port = builder.Port;
                var username = builder.Username;
                var password = builder.Password;
                
                // Create pg_restore command
                string restoreCommand = $"pg_restore -h {host} -p {port} -U {username} -d {databaseName} -c -v \"{backupFilePath}\"";
                
                // Execute pg_restore command
                var process = new Process
                {
                    StartInfo = new ProcessStartInfo
                    {
                        FileName = "cmd.exe",
                        Arguments = $"/c {restoreCommand}",
                        RedirectStandardOutput = true,
                        RedirectStandardError = true,
                        RedirectStandardInput = true,
                        UseShellExecute = false,
                        CreateNoWindow = true
                    }
                };
                
                process.Start();
                
                // Provide password if needed
                process.StandardInput.WriteLine(password);
                process.StandardInput.Close();
                
                // Wait for process to complete
                await process.WaitForExitAsync();
                
                if (process.ExitCode == 0)
                {
                    // Restore successful
                    await _auditService.LogActivityAsync(userId, "Database", "Restore", 
                        $"Restored database from backup: {Path.GetFileName(backupFilePath)}");
                    
                    return true;
                }
                else
                {
                    // Restore failed
                    string error = await process.StandardError.ReadToEndAsync();
                    await _errorHandlingService.LogErrorAsync("DatabaseBackupService.RestoreFromBackupAsync", 
                        $"Restore failed with exit code {process.ExitCode}", error);
                    
                    return false;
                }
            }
            catch (Exception ex)
            {
                await _errorHandlingService.LogErrorAsync("DatabaseBackupService.RestoreFromBackupAsync", 
                    ex.Message, ex.StackTrace);
                return false;
            }
        }
        
        /// <summary>
        /// Gets a list of available backups
        /// </summary>
        /// <returns>List of backup information</returns>
        public async Task<List<BackupInfo>> GetAvailableBackupsAsync()
        {
            try
            {
                var backupFiles = Directory.GetFiles(_backupPath, "*.backup")
                    .Select(file => new FileInfo(file))
                    .OrderByDescending(f => f.CreationTime);
                
                var backups = new List<BackupInfo>();
                
                foreach (var file in backupFiles)
                {
                    var match = Regex.Match(file.Name, @"^(Full|Differential)_(\d{8}_\d{6})\.backup$");
                    if (match.Success)
                    {
                        var backupType = match.Groups[1].Value == "Full" ? BackupType.Full : BackupType.Differential;
                        var timestamp = DateTime.ParseExact(
                            match.Groups[2].Value, 
                            "yyyyMMdd_HHmmss", 
                            CultureInfo.InvariantCulture);
                        
                        backups.Add(new BackupInfo
                        {
                            Id = Guid.NewGuid().ToString("N"), // Placeholder ID
                            FileName = file.Name,
                            FilePath = file.FullName,
                            CreatedAt = timestamp,
                            BackupType = backupType,
                            FileSize = file.Length,
                            Description = $"{backupType} backup created on {timestamp.ToString("yyyy-MM-dd HH:mm:ss")}"
                        });
                    }
                }
                
                return backups;
            }
            catch (Exception ex)
            {
                await _errorHandlingService.LogErrorAsync("DatabaseBackupService.GetAvailableBackupsAsync", 
                    ex.Message, ex.StackTrace);
                return new List<BackupInfo>();
            }
        }
        
        /// <summary>
        /// Verifies a backup file integrity
        /// </summary>
        /// <param name="backupFilePath">Path to the backup file</param>
        /// <returns>True if the backup is valid, otherwise false</returns>
        public async Task<bool> VerifyBackupAsync(string backupFilePath)
        {
            try
            {
                var result = await _backupVerificationService.VerifyBackupQuickAsync(backupFilePath);
                return result.Passed;
            }
            catch (Exception ex)
            {
                await _errorHandlingService.LogErrorAsync("DatabaseBackupService.VerifyBackupAsync", 
                    ex.Message, ex.StackTrace);
                return false;
            }
        }
        
        /// <summary>
        /// Performs a comprehensive verification of a backup file with test restore
        /// </summary>
        /// <param name="backupFilePath">Path to the backup file</param>
        /// <returns>Verification result with detailed information</returns>
        public async Task<BackupVerificationResult> VerifyBackupComprehensiveAsync(string backupFilePath)
        {
            try
            {
                // Use the dedicated backup verification service for comprehensive verification
                return await _backupVerificationService.VerifyBackupComprehensiveAsync(backupFilePath);
            }
            catch (Exception ex)
            {
                await _errorHandlingService.LogErrorAsync("DatabaseBackupService.VerifyBackupComprehensiveAsync", 
                    ex.Message, ex.StackTrace);
                
                return new BackupVerificationResult
                {
                    BackupFilePath = backupFilePath,
                    VerificationDate = DateTime.Now,
                    Status = VerificationStatus.Failed,
                    Details = new List<string> { $"Verification failed with error: {ex.Message}" }
                };
            }
        }
        
        /// <summary>
        /// Deletes a backup file
        /// </summary>
        /// <param name="userId">ID of the user performing the deletion</param>
        /// <param name="backupFilePath">Path to the backup file</param>
        /// <returns>True if successful, otherwise false</returns>
        public async Task<bool> DeleteBackupAsync(int userId, string backupFilePath)
        {
            try
            {
                if (!File.Exists(backupFilePath))
                {
                    await _errorHandlingService.LogErrorAsync("DatabaseBackupService.DeleteBackupAsync", 
                        "Backup file not found", backupFilePath);
                    return false;
                }
                
                // Delete the file
                File.Delete(backupFilePath);
                
                // Log backup deletion
                await _auditService.LogActivityAsync(userId, "Database", "Backup", 
                    $"Deleted database backup: {Path.GetFileName(backupFilePath)}");
                
                return true;
            }
            catch (Exception ex)
            {
                await _errorHandlingService.LogErrorAsync("DatabaseBackupService.DeleteBackupAsync", 
                    ex.Message, ex.StackTrace);
                return false;
            }
        }
        
        /// <summary>
        /// Saves backup metadata to the database
        /// </summary>
        private async Task SaveBackupMetadataAsync(BackupInfo backupInfo)
        {
            // In a real implementation, this would save the backup metadata to a database table
            // For now, we'll just write it to a JSON file
            try
            {
                var metadataPath = Path.Combine(_backupPath, "backup_metadata.json");
                var metadataList = new List<BackupInfo>();
                
                // Read existing metadata if file exists
                if (File.Exists(metadataPath))
                {
                    var metadataJson = await File.ReadAllTextAsync(metadataPath);
                    // In a real implementation, we would deserialize the JSON to a list of BackupInfo
                    // metadataList = JsonSerializer.Deserialize<List<BackupInfo>>(metadataJson);
                }
                
                // Add new backup metadata
                metadataList.Add(backupInfo);
                
                // Write updated metadata
                // var updatedJson = JsonSerializer.Serialize(metadataList, new JsonSerializerOptions { WriteIndented = true });
                // await File.WriteAllTextAsync(metadataPath, updatedJson);
            }
            catch (Exception ex)
            {
                await _errorHandlingService.LogErrorAsync("DatabaseBackupService.SaveBackupMetadataAsync", 
                    ex.Message, ex.StackTrace);
            }
        }
    }
    
    /// <summary>
    /// Types of database backups
    /// </summary>
    public enum BackupType
    {
        Full,
        Differential
    }
    

}
