using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using InventoryManagement.DataAccess;
using InventoryManagement.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace InventoryManagement.Services
{
    /// <summary>
    /// Base class for dashboard services to standardize common functionality
    /// and reduce code duplication across different dashboard implementations
    /// </summary>
    public abstract class BaseDashboardService
    {
        protected readonly ApplicationDbContext _dbContext;
        protected readonly ILogger _logger;
        protected readonly IAuditService _auditService;
        protected readonly IExceptionHandler _exceptionHandler;
        
        public BaseDashboardService(
            ApplicationDbContext dbContext,
            ILogger logger,
            IAuditService auditService,
            IExceptionHandler exceptionHandler)
        {
            _dbContext = dbContext ?? throw new ArgumentNullException(nameof(dbContext));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _auditService = auditService ?? throw new ArgumentNullException(nameof(auditService));
            _exceptionHandler = exceptionHandler ?? throw new ArgumentNullException(nameof(exceptionHandler));
        }
        
        #region Common Dashboard Data Retrieval
        
        /// <summary>
        /// Gets summary statistics common to all dashboards
        /// </summary>
        /// <returns>Dashboard summary data</returns>
        public virtual async Task<DashboardSummary> GetDashboardSummaryAsync()
        {
            try
            {
                var summary = new DashboardSummary
                {
                    TotalItems = await _dbContext.Items.CountAsync(i => i.IsActive),
                    LowStockItems = await _dbContext.Items
                        .CountAsync(i => i.IsActive && i.StockByLocation.Any(s => s.Quantity <= i.MinimumStockLevel)),
                    TotalLocations = await _dbContext.Locations.CountAsync(l => l.IsActive),
                    LastRefreshed = DateTime.Now
                };
                
                // Get today's transactions
                var today = DateTime.Today;
                summary.TodayTransactions = await _dbContext.Transactions
                    .CountAsync(t => t.TransactionDate.Date == today);
                
                return summary;
            }
            catch (Exception ex)
            {
                _exceptionHandler.HandleException(ex, "GetDashboardSummaryAsync");
                return new DashboardSummary();
            }
        }
        
        /// <summary>
        /// Gets recent activity for display on dashboards
        /// </summary>
        /// <param name="count">Number of activities to retrieve</param>
        /// <returns>Recent activities</returns>
        public virtual async Task<List<RecentActivity>> GetRecentActivitiesAsync(int count = 5)
        {
            try
            {
                var activities = new List<RecentActivity>();
                
                // Get recent transactions
                var recentTransactions = await _dbContext.Transactions
                    .Include(t => t.User)
                    .OrderByDescending(t => t.TransactionDate)
                    .Take(count)
                    .ToListAsync();
                    
                foreach (var transaction in recentTransactions)
                {
                    activities.Add(new RecentActivity
                    {
                        Id = transaction.Id,
                        Type = "Transaction",
                        Description = $"{transaction.Type} - {transaction.Amount:C}",
                        Timestamp = transaction.TransactionDate,
                        UserName = transaction.User?.UserName ?? "Unknown",
                        IconClass = GetIconForTransactionType(transaction.Type)
                    });
                }
                
                // Get recent inventory changes
                var recentStockChanges = await _dbContext.StockTransactions
                    .Include(s => s.Item)
                    .Include(s => s.User)
                    .OrderByDescending(s => s.TransactionDate)
                    .Take(count)
                    .ToListAsync();
                    
                foreach (var stockChange in recentStockChanges)
                {
                    activities.Add(new RecentActivity
                    {
                        Id = stockChange.Id,
                        Type = "Inventory",
                        Description = $"{stockChange.TransactionType} - {stockChange.Item?.Name ?? "Unknown Item"} ({stockChange.Quantity:N0})",
                        Timestamp = stockChange.TransactionDate,
                        UserName = stockChange.User?.UserName ?? "Unknown",
                        IconClass = GetIconForStockTransactionType(stockChange.TransactionType)
                    });
                }
                
                // Sort all activities by timestamp
                activities.Sort((a, b) => b.Timestamp.CompareTo(a.Timestamp));
                
                return activities.Take(count).ToList();
            }
            catch (Exception ex)
            {
                _exceptionHandler.HandleException(ex, "GetRecentActivitiesAsync");
                return new List<RecentActivity>();
            }
        }
        
        /// <summary>
        /// Gets alerts and notifications for display on dashboards
        /// </summary>
        /// <returns>Current alerts</returns>
        public virtual async Task<List<DashboardAlert>> GetAlertsAsync()
        {
            try
            {
                var alerts = new List<DashboardAlert>();
                
                // Low stock alerts
                var lowStockItems = await _dbContext.Items
                    .Include(i => i.StockByLocation)
                    .ThenInclude(s => s.Location)
                    .Where(i => i.IsActive && i.StockByLocation.Any(s => s.Quantity <= i.MinimumStockLevel))
                    .ToListAsync();
                    
                foreach (var item in lowStockItems)
                {
                    foreach (var stock in item.StockByLocation.Where(s => s.Quantity <= item.MinimumStockLevel))
                    {
                        alerts.Add(new DashboardAlert
                        {
                            Type = AlertType.LowStock,
                            Severity = AlertSeverity.Warning,
                            Title = "Low Stock",
                            Message = $"{item.Name} is low on stock at {stock.Location?.Name ?? "Unknown Location"} ({stock.Quantity} remaining)",
                            Timestamp = DateTime.Now,
                            ItemId = item.Id,
                            LocationId = stock.LocationId
                        });
                    }
                }
                
                // Expiring items alerts
                var thresholdDate = DateTime.Now.AddDays(30);
                var expiringItems = await _dbContext.ItemStocks
                    .Include(s => s.Item)
                    .Include(s => s.Location)
                    .Where(s => s.ExpirationDate.HasValue && s.ExpirationDate <= thresholdDate && s.Quantity > 0)
                    .ToListAsync();
                    
                foreach (var stock in expiringItems)
                {
                    var daysRemaining = (stock.ExpirationDate.Value - DateTime.Now).Days;
                    var severity = daysRemaining <= 7 ? AlertSeverity.Critical : AlertSeverity.Warning;
                    
                    alerts.Add(new DashboardAlert
                    {
                        Type = AlertType.ExpiringItem,
                        Severity = severity,
                        Title = "Expiring Item",
                        Message = $"{stock.Item?.Name ?? "Unknown Item"} expires in {daysRemaining} days at {stock.Location?.Name ?? "Unknown Location"}",
                        Timestamp = DateTime.Now,
                        ItemId = stock.ItemId,
                        LocationId = stock.LocationId,
                        ExpirationDate = stock.ExpirationDate
                    });
                }
                
                return alerts;
            }
            catch (Exception ex)
            {
                _exceptionHandler.HandleException(ex, "GetAlertsAsync");
                return new List<DashboardAlert>();
            }
        }
        
        #endregion
        
        #region Helper Methods
        
        /// <summary>
        /// Gets the icon class for a transaction type
        /// </summary>
        protected virtual string GetIconForTransactionType(string transactionType)
        {
            return transactionType.ToLower() switch
            {
                "sale" => "fa-shopping-cart",
                "return" => "fa-undo",
                "refund" => "fa-money-bill-wave",
                "payment" => "fa-credit-card",
                "adjustment" => "fa-balance-scale",
                _ => "fa-exchange-alt"
            };
        }
        
        /// <summary>
        /// Gets the icon class for a stock transaction type
        /// </summary>
        protected virtual string GetIconForStockTransactionType(string transactionType)
        {
            return transactionType.ToLower() switch
            {
                "receive" => "fa-truck",
                "transfer" => "fa-exchange-alt",
                "adjustment" => "fa-balance-scale",
                "writeoff" => "fa-trash",
                "return" => "fa-undo",
                _ => "fa-box"
            };
        }
        
        #endregion
    }
    
    #region Helper Classes
    
    /// <summary>
    /// Alert for display on dashboards
    /// </summary>
    public class DashboardAlert
    {
        public AlertType Type { get; set; }
        public AlertSeverity Severity { get; set; }
        public string Title { get; set; }
        public string Message { get; set; }
        public DateTime Timestamp { get; set; }
        public int? ItemId { get; set; }
        public int? LocationId { get; set; }
        public DateTime? ExpirationDate { get; set; }
    }
    
    /// <summary>
    /// Types of alerts
    /// </summary>
    public enum AlertType
    {
        LowStock,
        ExpiringItem,
        PriceChange,
        SystemMessage,
        SecurityAlert
    }
    
    /// <summary>
    /// Severity levels for alerts
    /// </summary>
    public enum AlertSeverity
    {
        Information,
        Warning,
        Critical
    }
    
    #endregion
}
