using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Windows.Input;

namespace InventoryManagement.ViewModels.Base
{
    /// <summary>
    /// Base class for all ViewModels providing common functionality
    /// </summary>
    public abstract class BaseViewModel : INotifyPropertyChanged, IDisposable
    {
        private bool _isLoading;
        private string _errorMessage = string.Empty;
        private string _successMessage = string.Empty;
        private string _warningMessage = string.Empty;
        private string _infoMessage = string.Empty;

        #region Properties

        /// <summary>
        /// Indicates if the ViewModel is currently loading data
        /// </summary>
        public bool IsLoading
        {
            get => _isLoading;
            set => SetProperty(ref _isLoading, value);
        }

        /// <summary>
        /// Current error message to display to the user
        /// </summary>
        public string ErrorMessage
        {
            get => _errorMessage;
            set => SetProperty(ref _errorMessage, value);
        }

        /// <summary>
        /// Current success message to display to the user
        /// </summary>
        public string SuccessMessage
        {
            get => _successMessage;
            set => SetProperty(ref _successMessage, value);
        }

        /// <summary>
        /// Current warning message to display to the user
        /// </summary>
        public string WarningMessage
        {
            get => _warningMessage;
            set => SetProperty(ref _warningMessage, value);
        }

        /// <summary>
        /// Current info message to display to the user
        /// </summary>
        public string InfoMessage
        {
            get => _infoMessage;
            set => SetProperty(ref _infoMessage, value);
        }

        /// <summary>
        /// Indicates if there are any messages to display
        /// </summary>
        public bool HasMessages => !string.IsNullOrEmpty(ErrorMessage) || 
                                  !string.IsNullOrEmpty(SuccessMessage) || 
                                  !string.IsNullOrEmpty(WarningMessage) || 
                                  !string.IsNullOrEmpty(InfoMessage);

        #endregion

        #region Commands

        public ICommand ClearMessagesCommand { get; }

        #endregion

        #region Constructor

        protected BaseViewModel()
        {
            ClearMessagesCommand = new RelayCommand(() => ClearMessages());
        }

        #endregion

        #region INotifyPropertyChanged Implementation

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        protected bool SetProperty<T>(ref T field, T value, [CallerMemberName] string? propertyName = null)
        {
            if (EqualityComparer<T>.Default.Equals(field, value))
                return false;

            field = value;
            OnPropertyChanged(propertyName);
            return true;
        }

        #endregion

        #region Message Methods

        /// <summary>
        /// Shows an error message to the user
        /// </summary>
        /// <param name="message">Error message</param>
        protected void ShowErrorMessage(string message)
        {
            ClearMessages();
            ErrorMessage = message;
            OnPropertyChanged(nameof(HasMessages));
        }

        /// <summary>
        /// Shows a success message to the user
        /// </summary>
        /// <param name="message">Success message</param>
        protected void ShowSuccessMessage(string message)
        {
            ClearMessages();
            SuccessMessage = message;
            OnPropertyChanged(nameof(HasMessages));
        }

        /// <summary>
        /// Shows a warning message to the user
        /// </summary>
        /// <param name="message">Warning message</param>
        protected void ShowWarningMessage(string message)
        {
            ClearMessages();
            WarningMessage = message;
            OnPropertyChanged(nameof(HasMessages));
        }

        /// <summary>
        /// Shows an info message to the user
        /// </summary>
        /// <param name="message">Info message</param>
        protected void ShowInfoMessage(string message)
        {
            ClearMessages();
            InfoMessage = message;
            OnPropertyChanged(nameof(HasMessages));
        }

        /// <summary>
        /// Clears all messages
        /// </summary>
        protected void ClearMessages()
        {
            ErrorMessage = string.Empty;
            SuccessMessage = string.Empty;
            WarningMessage = string.Empty;
            InfoMessage = string.Empty;
            OnPropertyChanged(nameof(HasMessages));
        }

        /// <summary>
        /// Shows a confirmation dialog to the user
        /// </summary>
        /// <param name="message">Message to display</param>
        /// <param name="title">Dialog title</param>
        /// <returns>True if user confirmed, false otherwise</returns>
        protected virtual bool ShowConfirmationDialog(string message, string title = "Confirm")
        {
            // This would typically use a dialog service
            // For now, return true as a placeholder
            return true;
        }

        #endregion

        #region Validation

        /// <summary>
        /// Validates the current state of the ViewModel
        /// </summary>
        /// <returns>True if valid, false otherwise</returns>
        public virtual bool IsValid()
        {
            return true;
        }

        /// <summary>
        /// Gets validation errors for the ViewModel
        /// </summary>
        /// <returns>List of validation errors</returns>
        public virtual List<string> GetValidationErrors()
        {
            return new List<string>();
        }

        #endregion

        #region Async Operations

        /// <summary>
        /// Executes an async operation with loading state management
        /// </summary>
        /// <param name="operation">Operation to execute</param>
        /// <param name="errorMessage">Error message to show if operation fails</param>
        protected async Task ExecuteAsync(Func<Task> operation, string? errorMessage = null)
        {
            try
            {
                IsLoading = true;
                ClearMessages();
                await operation();
            }
            catch (Exception ex)
            {
                ShowErrorMessage(errorMessage ?? ex.Message);
            }
            finally
            {
                IsLoading = false;
            }
        }

        /// <summary>
        /// Executes an async operation with loading state management and return value
        /// </summary>
        /// <typeparam name="T">Return type</typeparam>
        /// <param name="operation">Operation to execute</param>
        /// <param name="errorMessage">Error message to show if operation fails</param>
        /// <returns>Result of the operation or default value if failed</returns>
        protected async Task<T?> ExecuteAsync<T>(Func<Task<T>> operation, string? errorMessage = null)
        {
            try
            {
                IsLoading = true;
                ClearMessages();
                return await operation();
            }
            catch (Exception ex)
            {
                ShowErrorMessage(errorMessage ?? ex.Message);
                return default;
            }
            finally
            {
                IsLoading = false;
            }
        }

        #endregion

        #region IDisposable Implementation

        private bool _disposed = false;

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed && disposing)
            {
                // Dispose managed resources
                _disposed = true;
            }
        }

        #endregion
    }

    /// <summary>
    /// Simple implementation of ICommand for use in ViewModels
    /// </summary>
    public class RelayCommand : ICommand
    {
        private readonly Action _execute;
        private readonly Func<bool>? _canExecute;

        public RelayCommand(Action execute, Func<bool>? canExecute = null)
        {
            _execute = execute ?? throw new ArgumentNullException(nameof(execute));
            _canExecute = canExecute;
        }

        public event EventHandler? CanExecuteChanged
        {
            add { CommandManager.RequerySuggested += value; }
            remove { CommandManager.RequerySuggested -= value; }
        }

        public bool CanExecute(object? parameter)
        {
            return _canExecute?.Invoke() ?? true;
        }

        public void Execute(object? parameter)
        {
            _execute();
        }

        public void RaiseCanExecuteChanged()
        {
            CommandManager.InvalidateRequerySuggested();
        }
    }

    /// <summary>
    /// Generic implementation of ICommand for use in ViewModels
    /// </summary>
    /// <typeparam name="T">Parameter type</typeparam>
    public class RelayCommand<T> : ICommand
    {
        private readonly Action<T?> _execute;
        private readonly Func<T?, bool>? _canExecute;

        public RelayCommand(Action<T?> execute, Func<T?, bool>? canExecute = null)
        {
            _execute = execute ?? throw new ArgumentNullException(nameof(execute));
            _canExecute = canExecute;
        }

        public event EventHandler? CanExecuteChanged
        {
            add { CommandManager.RequerySuggested += value; }
            remove { CommandManager.RequerySuggested -= value; }
        }

        public bool CanExecute(object? parameter)
        {
            return _canExecute?.Invoke((T?)parameter) ?? true;
        }

        public void Execute(object? parameter)
        {
            _execute((T?)parameter);
        }

        public void RaiseCanExecuteChanged()
        {
            CommandManager.InvalidateRequerySuggested();
        }
    }
}
