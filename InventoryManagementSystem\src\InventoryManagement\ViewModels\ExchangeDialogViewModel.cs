using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using System.Windows.Media;
using Microsoft.EntityFrameworkCore;
using InventoryManagement.Commands;
using InventoryManagement.DataAccess;
using InventoryManagement.Models;
using InventoryManagement.Services;
using InventoryManagement.ViewModels.Shared;

namespace InventoryManagement.ViewModels
{
    /// <summary>
    /// ViewModel for the Exchange Dialog to handle item exchanges, returns, and replacements
    /// </summary>
    public class ExchangeDialogViewModel : ViewModelBase
    {
        private readonly ApplicationDbContext _dbContext;
        private readonly IAuditService _auditService;
        private readonly INotificationService _notificationService;
        private readonly User _currentUser;
        private readonly EnhancedExceptionHandler _exceptionHandler;
        
        private readonly ItemExchange _exchange;
        private readonly bool _isNewExchange;
        
        private string _windowTitle;
        private ObservableCollection<string> _exchangeTypes;
        private string _selectedExchangeType;
        private string _customerInfo;
        private string _originalTransactionNumber;
        private string _transactionValidationMessage;
        private Brush _transactionValidationColor;
        private ObservableCollection<Location> _locations;
        private Location _selectedLocation;
        private DateTime _exchangeDate;
        private ObservableCollection<string> _statusOptions;
        private string _selectedStatus;
        private ObservableCollection<ItemViewModel> _availableItems;
        private ItemViewModel _selectedItem;
        private string _sku;
        private decimal _quantity;
        private string _quantityUnit = "Units";
        private ObservableCollection<string> _reasonOptions;
        private string _selectedReason;
        private string _notes;
        private ObservableCollection<ItemViewModel> _availableReplacementItems;
        private ItemViewModel _selectedReplacementItem;
        private decimal _originalValue;
        private decimal _returnValue;
        private decimal _replacementValue;
        private decimal _netImpact;
        private Brush _netImpactColor;
        private bool _isBusy;
        
        /// <summary>
        /// Creates a new ExchangeDialogViewModel
        /// </summary>
        /// <param name="exchange">The exchange to edit or null for a new exchange</param>
        /// <param name="isNewExchange">True if this is a new exchange, false if editing an existing one</param>
        /// <param name="dbContext">Database context</param>
        /// <param name="auditService">Audit service for logging actions</param>
        /// <param name="notificationService">Notification service</param>
        /// <param name="userService">User service</param>
        /// <param name="exceptionHandler">Exception handler</param>
        public ExchangeDialogViewModel(
            ItemExchange exchange,
            bool isNewExchange,
            ApplicationDbContext dbContext,
            IAuditService auditService,
            INotificationService notificationService,
            IUserService userService,
            EnhancedExceptionHandler exceptionHandler)
        {
            _exchange = exchange ?? new ItemExchange();
            _isNewExchange = isNewExchange;
            _dbContext = dbContext ?? throw new ArgumentNullException(nameof(dbContext));
            _auditService = auditService ?? throw new ArgumentNullException(nameof(auditService));
            _notificationService = notificationService ?? throw new ArgumentNullException(nameof(notificationService));
            _currentUser = userService?.GetCurrentUser() ?? throw new ArgumentNullException(nameof(userService));
            _exceptionHandler = exceptionHandler ?? throw new ArgumentNullException(nameof(exceptionHandler));
            
            WindowTitle = isNewExchange ? "Create New Exchange" : "Edit Exchange";
            
            // Initialize collections
            ExchangeTypes = new ObservableCollection<string>(new[] { 
                "Return", 
                "Replacement", 
                "Refund" 
            });
            
            Locations = new ObservableCollection<Location>();
            
            StatusOptions = new ObservableCollection<string>(new[] { 
                "Pending", 
                "Processing", 
                "Approved", 
                "Completed", 
                "Rejected", 
                "Cancelled" 
            });
            
            ReasonOptions = new ObservableCollection<string>(new[] { 
                "Defective Product", 
                "Wrong Item", 
                "Customer Dissatisfaction", 
                "Better Price Found Elsewhere", 
                "Size/Color Issue", 
                "Changed Mind", 
                "Other" 
            });
            
            AvailableItems = new ObservableCollection<ItemViewModel>();
            AvailableReplacementItems = new ObservableCollection<ItemViewModel>();
            
            // Initialize default validation messages
            TransactionValidationMessage = "Enter a transaction number and click Find to validate";
            TransactionValidationColor = Brushes.Gray;
            
            // Initialize commands
            FindTransactionCommand = new RelayCommand(_ => FindTransaction());
            SaveCommand = new RelayCommand(_ => Save(), _ => CanSave());
            CancelCommand = new RelayCommand(_ => Cancel());
            
            // Set default values for new exchanges
            if (isNewExchange)
            {
                ExchangeDate = DateTime.Now;
                SelectedExchangeType = "Return";
                SelectedStatus = "Pending";
                SelectedReason = "Defective Product";
                _exchange.ExchangeDate = DateTime.Now;
                _exchange.InitiatedByUserId = _currentUser.Id;
                _exchange.InitiatedByUserName = _currentUser.FullName;
                
                // Default quantity to 1
                Quantity = 1;
            }
            else
            {
                // Load existing exchange values
                LoadExistingExchange();
            }
            
            // Initialize financial colors
            _netImpactColor = Brushes.Black;
            
            // Load data
            LoadDataAsync();
        }
        
        #region Properties
        
        public string WindowTitle
        {
            get => _windowTitle;
            set => SetProperty(ref _windowTitle, value);
        }
        
        public ObservableCollection<string> ExchangeTypes
        {
            get => _exchangeTypes;
            set => SetProperty(ref _exchangeTypes, value);
        }
        
        public string SelectedExchangeType
        {
            get => _selectedExchangeType;
            set
            {
                if (SetProperty(ref _selectedExchangeType, value))
                {
                    _exchange.ExchangeType = value;
                    OnPropertyChanged(nameof(IsReplacement));
                    OnPropertyChanged(nameof(ReplacementValueVisibility));
                    
                    // Update financial calculations
                    UpdateFinancialCalculations();
                }
            }
        }
        
        public string CustomerInfo
        {
            get => _customerInfo;
            set
            {
                if (SetProperty(ref _customerInfo, value))
                {
                    _exchange.CustomerInfo = value;
                }
            }
        }
        
        public string OriginalTransactionNumber
        {
            get => _originalTransactionNumber;
            set
            {
                if (SetProperty(ref _originalTransactionNumber, value))
                {
                    _exchange.OriginalTransactionNumber = value;
                    
                    // Reset validation message
                    TransactionValidationMessage = "Enter a transaction number and click Find to validate";
                    TransactionValidationColor = Brushes.Gray;
                }
            }
        }
        
        public string TransactionValidationMessage
        {
            get => _transactionValidationMessage;
            set => SetProperty(ref _transactionValidationMessage, value);
        }
        
        public Brush TransactionValidationColor
        {
            get => _transactionValidationColor;
            set => SetProperty(ref _transactionValidationColor, value);
        }
        
        public ObservableCollection<Location> Locations
        {
            get => _locations;
            set => SetProperty(ref _locations, value);
        }
        
        public Location SelectedLocation
        {
            get => _selectedLocation;
            set
            {
                if (SetProperty(ref _selectedLocation, value) && value != null)
                {
                    _exchange.LocationId = value.Id;
                    _exchange.LocationName = value.Name;
                    
                    // Load items for this location
                    LoadItemsForLocation(value.Id);
                }
            }
        }
        
        public DateTime ExchangeDate
        {
            get => _exchangeDate;
            set
            {
                if (SetProperty(ref _exchangeDate, value))
                {
                    _exchange.ExchangeDate = value;
                }
            }
        }
        
        public ObservableCollection<string> StatusOptions
        {
            get => _statusOptions;
            set => SetProperty(ref _statusOptions, value);
        }
        
        public string SelectedStatus
        {
            get => _selectedStatus;
            set
            {
                if (SetProperty(ref _selectedStatus, value))
                {
                    _exchange.Status = value;
                }
            }
        }
        
        public ObservableCollection<ItemViewModel> AvailableItems
        {
            get => _availableItems;
            set => SetProperty(ref _availableItems, value);
        }
        
        public ItemViewModel SelectedItem
        {
            get => _selectedItem;
            set
            {
                if (SetProperty(ref _selectedItem, value) && value != null)
                {
                    _exchange.ItemId = value.Id;
                    _exchange.ItemName = value.Name;
                    
                    // Update SKU
                    SKU = value.SKU;
                    
                    // Update financial calculations
                    UpdateFinancialCalculations();
                }
            }
        }
        
        public string SKU
        {
            get => _sku;
            set => SetProperty(ref _sku, value);
        }
        
        public decimal Quantity
        {
            get => _quantity;
            set
            {
                if (SetProperty(ref _quantity, value))
                {
                    _exchange.Quantity = value;
                    
                    // Update financial calculations
                    UpdateFinancialCalculations();
                }
            }
        }
        
        public string QuantityUnit
        {
            get => _quantityUnit;
            set => SetProperty(ref _quantityUnit, value);
        }
        
        public ObservableCollection<string> ReasonOptions
        {
            get => _reasonOptions;
            set => SetProperty(ref _reasonOptions, value);
        }
        
        public string SelectedReason
        {
            get => _selectedReason;
            set
            {
                if (SetProperty(ref _selectedReason, value))
                {
                    _exchange.Reason = value;
                }
            }
        }
        
        public string Notes
        {
            get => _notes;
            set
            {
                if (SetProperty(ref _notes, value))
                {
                    _exchange.Notes = value;
                }
            }
        }
        
        public ObservableCollection<ItemViewModel> AvailableReplacementItems
        {
            get => _availableReplacementItems;
            set => SetProperty(ref _availableReplacementItems, value);
        }
        
        public ItemViewModel SelectedReplacementItem
        {
            get => _selectedReplacementItem;
            set
            {
                if (SetProperty(ref _selectedReplacementItem, value))
                {
                    if (value != null)
                    {
                        _exchange.ReplacementItemId = value.Id;
                        _exchange.ReplacementItemName = value.Name;
                    }
                    else
                    {
                        _exchange.ReplacementItemId = null;
                        _exchange.ReplacementItemName = null;
                    }
                    
                    // Update financial calculations
                    UpdateFinancialCalculations();
                }
            }
        }
        
        public decimal OriginalValue
        {
            get => _originalValue;
            set => SetProperty(ref _originalValue, value);
        }
        
        public decimal ReturnValue
        {
            get => _returnValue;
            set => SetProperty(ref _returnValue, value);
        }
        
        public decimal ReplacementValue
        {
            get => _replacementValue;
            set => SetProperty(ref _replacementValue, value);
        }
        
        public decimal NetImpact
        {
            get => _netImpact;
            set
            {
                if (SetProperty(ref _netImpact, value))
                {
                    // Update color based on value
                    NetImpactColor = value < 0 ? Brushes.Red : (value > 0 ? Brushes.Green : Brushes.Black);
                    
                    // Update exchange financial values
                    _exchange.OriginalValue = OriginalValue;
                    _exchange.ReturnValue = ReturnValue;
                    _exchange.ReplacementValue = ReplacementValue;
                    _exchange.NetImpact = value;
                }
            }
        }
        
        public Brush NetImpactColor
        {
            get => _netImpactColor;
            set => SetProperty(ref _netImpactColor, value);
        }
        
        public bool IsBusy
        {
            get => _isBusy;
            set => SetProperty(ref _isBusy, value);
        }
        
        public bool IsNewExchange => _isNewExchange;
        
        public bool IsReplacement => SelectedExchangeType == "Replacement";
        
        public Visibility ReplacementValueVisibility => IsReplacement ? Visibility.Visible : Visibility.Collapsed;
        
        #endregion
        
        #region Commands
        
        public ICommand FindTransactionCommand { get; }
        public ICommand SaveCommand { get; }
        public ICommand CancelCommand { get; }
        
        #endregion
        
        #region Methods
        
        private async void LoadDataAsync()
        {
            try
            {
                IsBusy = true;
                
                // Load locations
                await LoadLocationsAsync();
                
                // If editing an existing exchange, select the appropriate location
                if (!_isNewExchange && _exchange.Id > 0)
                {
                    SelectedLocation = Locations.FirstOrDefault(l => l.Id == _exchange.LocationId);
                    
                    // Load replacement items if needed
                    if (IsReplacement && _exchange.ReplacementItemId.HasValue)
                    {
                        await LoadReplacementItemsAsync();
                        SelectedReplacementItem = AvailableReplacementItems.FirstOrDefault(i => i.Id == _exchange.ReplacementItemId);
                    }
                }
                else if (Locations.Count > 0)
                {
                    // For new exchanges, select the first location by default
                    SelectedLocation = Locations.FirstOrDefault();
                }
            }
            catch (Exception ex)
            {
                await _exceptionHandler.HandleExceptionAsync(ex, "Error loading exchange data");
            }
            finally
            {
                IsBusy = false;
            }
        }
        
        private async Task LoadLocationsAsync()
        {
            try
            {
                // Get all active locations
                var locations = await _dbContext.Locations
                    .Where(l => l.IsActive)
                    .OrderBy(l => l.Name)
                    .ToListAsync();
                
                // Update locations collection
                Locations.Clear();
                foreach (var location in locations)
                {
                    Locations.Add(location);
                }
            }
            catch (Exception ex)
            {
                await _exceptionHandler.HandleExceptionAsync(ex, "Error loading locations");
            }
        }
        
        private async void LoadItemsForLocation(int locationId)
        {
            try
            {
                // Get items with stock in the selected location
                var itemsWithStock = await _dbContext.ItemStocks
                    .Where(s => s.LocationId == locationId && s.Quantity > 0)
                    .Join(_dbContext.Items,
                        stock => stock.ItemId,
                        item => item.Id,
                        (stock, item) => new
                        {
                            Item = item,
                            AvailableQuantity = stock.Quantity
                        })
                    .Where(x => !x.Item.IsDeleted && x.Item.IsActive)
                    .OrderBy(x => x.Item.Name)
                    .ToListAsync();
                
                // Update available items collection
                AvailableItems.Clear();
                foreach (var itemStock in itemsWithStock)
                {
                    var itemViewModel = new ItemViewModel
                    {
                        Id = itemStock.Item.Id,
                        Name = itemStock.Item.Name,
                        SKU = itemStock.Item.SKU,
                        Category = itemStock.Item.Category,
                        AvailableQuantity = itemStock.AvailableQuantity,
                        RetailPrice = itemStock.Item.RetailPrice
                    };
                    
                    AvailableItems.Add(itemViewModel);
                }
                
                // If editing an existing exchange, select the appropriate item
                if (!_isNewExchange && _exchange.ItemId > 0)
                {
                    SelectedItem = AvailableItems.FirstOrDefault(i => i.Id == _exchange.ItemId);
                }
                else if (AvailableItems.Count > 0)
                {
                    // For new exchanges, select the first item by default
                    SelectedItem = AvailableItems.FirstOrDefault();
                }
                
                // Load replacement items if this is a replacement
                if (IsReplacement)
                {
                    await LoadReplacementItemsAsync();
                }
            }
            catch (Exception ex)
            {
                await _exceptionHandler.HandleExceptionAsync(ex, "Error loading items for location");
            }
        }
        
        private async Task LoadReplacementItemsAsync()
        {
            try
            {
                // Get all active items
                var items = await _dbContext.Items
                    .Where(i => !i.IsDeleted && i.IsActive)
                    .OrderBy(i => i.Name)
                    .ToListAsync();
                
                // Update available replacement items collection
                AvailableReplacementItems.Clear();
                foreach (var item in items)
                {
                    var itemViewModel = new ItemViewModel
                    {
                        Id = item.Id,
                        Name = item.Name,
                        SKU = item.SKU,
                        Category = item.Category,
                        RetailPrice = item.RetailPrice
                    };
                    
                    AvailableReplacementItems.Add(itemViewModel);
                }
                
                // If editing an existing exchange, select the appropriate replacement item
                if (!_isNewExchange && _exchange.ReplacementItemId.HasValue)
                {
                    SelectedReplacementItem = AvailableReplacementItems.FirstOrDefault(i => i.Id == _exchange.ReplacementItemId);
                }
            }
            catch (Exception ex)
            {
                await _exceptionHandler.HandleExceptionAsync(ex, "Error loading replacement items");
            }
        }
        
        private void LoadExistingExchange()
        {
            // Set properties from existing exchange
            SelectedExchangeType = _exchange.ExchangeType;
            CustomerInfo = _exchange.CustomerInfo;
            OriginalTransactionNumber = _exchange.OriginalTransactionNumber;
            ExchangeDate = _exchange.ExchangeDate;
            SelectedStatus = _exchange.Status;
            Quantity = _exchange.Quantity;
            SelectedReason = _exchange.Reason;
            Notes = _exchange.Notes;
            
            // Set financial values
            OriginalValue = _exchange.OriginalValue;
            ReturnValue = _exchange.ReturnValue;
            ReplacementValue = _exchange.ReplacementValue;
            NetImpact = _exchange.NetImpact;
        }
        
        private async void FindTransaction()
        {
            if (string.IsNullOrWhiteSpace(OriginalTransactionNumber))
            {
                TransactionValidationMessage = "Please enter a transaction number";
                TransactionValidationColor = Brushes.Red;
                return;
            }
            
            try
            {
                IsBusy = true;
                
                // Find the transaction
                var transaction = await _dbContext.Transactions
                    .FirstOrDefaultAsync(t => t.TransactionNumber == OriginalTransactionNumber);
                
                if (transaction == null)
                {
                    TransactionValidationMessage = "Transaction not found";
                    TransactionValidationColor = Brushes.Red;
                    return;
                }
                
                // Get transaction details
                var transactionDetails = await _dbContext.TransactionDetails
                    .Where(td => td.TransactionId == transaction.Id)
                    .ToListAsync();
                
                if (!transactionDetails.Any())
                {
                    TransactionValidationMessage = "Transaction found but contains no items";
                    TransactionValidationColor = Brushes.Yellow;
                    return;
                }
                
                // Transaction is valid
                TransactionValidationMessage = "Transaction found and valid";
                TransactionValidationColor = Brushes.Green;
                
                // If this is a new exchange, auto-populate customer info
                if (_isNewExchange && string.IsNullOrWhiteSpace(CustomerInfo))
                {
                    CustomerInfo = transaction.CustomerInfo;
                }
            }
            catch (Exception ex)
            {
                await _exceptionHandler.HandleExceptionAsync(ex, "Error finding transaction");
                TransactionValidationMessage = "Error finding transaction";
                TransactionValidationColor = Brushes.Red;
            }
            finally
            {
                IsBusy = false;
            }
        }
        
        private void UpdateFinancialCalculations()
        {
            if (SelectedItem == null)
                return;
            
            // Calculate return value
            ReturnValue = SelectedItem.RetailPrice * Quantity;
            
            // Calculate replacement value if applicable
            if (IsReplacement && SelectedReplacementItem != null)
            {
                ReplacementValue = SelectedReplacementItem.RetailPrice * Quantity;
            }
            else
            {
                ReplacementValue = 0;
            }
            
            // Calculate original value (same as return value for now)
            OriginalValue = ReturnValue;
            
            // Calculate net impact based on exchange type
            switch (SelectedExchangeType)
            {
                case "Return":
                    // Return reduces inventory value (negative impact)
                    NetImpact = -ReturnValue;
                    break;
                case "Replacement":
                    // Replacement: return - replacement (usually negative or zero)
                    NetImpact = ReturnValue - ReplacementValue;
                    break;
                case "Refund":
                    // Refund is a full return (negative impact)
                    NetImpact = -ReturnValue;
                    break;
                default:
                    NetImpact = 0;
                    break;
            }
        }
        
        private bool CanSave()
        {
            // Basic validation
            if (SelectedItem == null || SelectedLocation == null)
                return false;
            
            if (Quantity <= 0)
                return false;
            
            if (string.IsNullOrWhiteSpace(SelectedExchangeType) || 
                string.IsNullOrWhiteSpace(SelectedStatus) || 
                string.IsNullOrWhiteSpace(SelectedReason))
                return false;
            
            // For replacements, require a replacement item
            if (IsReplacement && SelectedReplacementItem == null)
                return false;
            
            return true;
        }
        
        public void Cancel()
        {
            // Set dialog result
            DialogResult = false;
        }
        
        public bool? DialogResult { get; set; }
        
        public async void Save()
        {
            try
            {
                // Validate
                if (!CanSave())
                    return;
                
                IsBusy = true;
                
                // Ensure all properties are set
                _exchange.ExchangeType = SelectedExchangeType;
                _exchange.CustomerInfo = CustomerInfo;
                _exchange.OriginalTransactionNumber = OriginalTransactionNumber;
                _exchange.LocationId = SelectedLocation.Id;
                _exchange.LocationName = SelectedLocation.Name;
                _exchange.ExchangeDate = ExchangeDate;
                _exchange.Status = SelectedStatus;
                _exchange.ItemId = SelectedItem.Id;
                _exchange.ItemName = SelectedItem.Name;
                _exchange.Quantity = Quantity;
                _exchange.Reason = SelectedReason;
                _exchange.Notes = Notes;
                
                // Set replacement item if applicable
                if (IsReplacement && SelectedReplacementItem != null)
                {
                    _exchange.ReplacementItemId = SelectedReplacementItem.Id;
                    _exchange.ReplacementItemName = SelectedReplacementItem.Name;
                }
                else
                {
                    _exchange.ReplacementItemId = null;
                    _exchange.ReplacementItemName = null;
                }
                
                // Ensure financial values are set
                _exchange.OriginalValue = OriginalValue;
                _exchange.ReturnValue = ReturnValue;
                _exchange.ReplacementValue = ReplacementValue;
                _exchange.NetImpact = NetImpact;
                
                // Set audit fields for new exchanges
                if (_isNewExchange)
                {
                    _exchange.InitiatedByUserId = _currentUser.Id;
                    _exchange.InitiatedByUserName = _currentUser.FullName;
                    _exchange.CreatedDate = DateTime.Now;
                }
                
                // Set last modified fields
                _exchange.LastModifiedDate = DateTime.Now;
                _exchange.LastModifiedByUserId = _currentUser.Id;
                _exchange.LastModifiedByUserName = _currentUser.FullName;
                
                // Save to database
                if (_isNewExchange)
                {
                    await _dbContext.ItemExchanges.AddAsync(_exchange);
                }
                else
                {
                    _dbContext.ItemExchanges.Update(_exchange);
                }
                
                await _dbContext.SaveChangesAsync();
                
                // Log audit trail
                string action = _isNewExchange ? "Create" : "Update";
                await _auditService.LogActionAsync(
                    "ItemExchange",
                    action,
                    $"{action}d {SelectedExchangeType} for {Quantity} {SelectedItem.Name} at {SelectedLocation.Name}",
                    _exchange.Id.ToString());
                
                // Send notification to relevant parties
                await SendExchangeNotificationAsync();
                
                // Set dialog result
                DialogResult = true;
            }
            catch (Exception ex)
            {
                await _exceptionHandler.HandleExceptionAsync(ex, "Error saving exchange");
                DialogResult = false;
            }
            finally
            {
                IsBusy = false;
            }
        }
        
        private async Task SendExchangeNotificationAsync()
        {
            try
            {
                // Get basement managers (they typically handle exchanges)
                var managers = await _dbContext.Users
                    .Where(u => u.Role == UserRole.BasementManager && u.IsActive)
                    .ToListAsync();
                
                if (managers.Any())
                {
                    // Create notification for each manager
                    foreach (var manager in managers)
                    {
                        var notification = new Notification
                        {
                            Title = $"New {SelectedExchangeType} Request",
                            Message = $"{SelectedExchangeType} request for {Quantity} {SelectedItem.Name} at {SelectedLocation.Name}. Reason: {SelectedReason}",
                            Type = NotificationType.ItemExchange,
                            UserId = manager.Id,
                            SourceUserId = _currentUser.Id,
                            CreatedDate = DateTime.Now,
                            Priority = NotificationPriority.Normal,
                            IsRead = false,
                            RelatedEntityId = _exchange.Id.ToString()
                        };
                        
                        await _notificationService.SendNotificationAsync(notification);
                    }
                }
            }
            catch (Exception ex)
            {
                await _exceptionHandler.HandleExceptionAsync(ex, "Error sending exchange notification");
            }
        }
        
        /// <summary>
        /// Returns the exchange object with all updated values
        /// </summary>
        /// <returns>The updated exchange</returns>
        public ItemExchange GetExchange()
        {
            return _exchange;
        }
        
        #endregion
    }
}
