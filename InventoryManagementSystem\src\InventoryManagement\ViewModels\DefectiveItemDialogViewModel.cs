using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.IO;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using Microsoft.EntityFrameworkCore;
using Microsoft.Win32;
using InventoryManagement.Commands;
using InventoryManagement.DataAccess;
using InventoryManagement.Models;
using InventoryManagement.Services;
using InventoryManagement.ViewModels.Shared;

namespace InventoryManagement.ViewModels
{
    /// <summary>
    /// ViewModel for the Defective Item Dialog to report and process defective items
    /// </summary>
    public class DefectiveItemDialogViewModel : ViewModelBase
    {
        private readonly ApplicationDbContext _dbContext;
        private readonly IAuditService _auditService;
        private readonly INotificationService _notificationService;
        private readonly User _currentUser;
        private readonly EnhancedExceptionHandler _exceptionHandler;
        
        private readonly DefectiveItem _defectiveItem;
        private readonly bool _isNewReport;
        
        private string _windowTitle;
        private ObservableCollection<ItemViewModel> _availableItems;
        private ItemViewModel _selectedItem;
        private string _sku;
        private string _barcode;
        private ObservableCollection<Location> _locations;
        private Location _selectedLocation;
        private decimal _quantity;
        private decimal _availableQuantity;
        private string _quantityUnit = "Units";
        private DateTime _reportDate;
        private ObservableCollection<string> _defectTypes;
        private string _selectedDefectType;
        private ObservableCollection<string> _severityLevels;
        private string _selectedSeverity;
        private ObservableCollection<string> _statusOptions;
        private string _selectedStatus;
        private string _description;
        private ObservableCollection<string> _actionOptions;
        private string _selectedAction;
        private string _photoPath;
        private bool _sendNotification = true;
        private decimal _costValue;
        private decimal _retailValue;
        private bool _isBusy;
        
        /// <summary>
        /// Creates a new DefectiveItemDialogViewModel
        /// </summary>
        /// <param name="defectiveItem">The defective item to edit or null for a new report</param>
        /// <param name="isNewReport">True if this is a new report, false if editing an existing one</param>
        /// <param name="dbContext">Database context</param>
        /// <param name="auditService">Audit service for logging actions</param>
        /// <param name="notificationService">Notification service</param>
        /// <param name="userService">User service</param>
        /// <param name="exceptionHandler">Exception handler</param>
        public DefectiveItemDialogViewModel(
            DefectiveItem defectiveItem,
            bool isNewReport,
            ApplicationDbContext dbContext,
            IAuditService auditService,
            INotificationService notificationService,
            IUserService userService,
            EnhancedExceptionHandler exceptionHandler)
        {
            _defectiveItem = defectiveItem ?? new DefectiveItem();
            _isNewReport = isNewReport;
            _dbContext = dbContext ?? throw new ArgumentNullException(nameof(dbContext));
            _auditService = auditService ?? throw new ArgumentNullException(nameof(auditService));
            _notificationService = notificationService ?? throw new ArgumentNullException(nameof(notificationService));
            _currentUser = userService?.GetCurrentUser() ?? throw new ArgumentNullException(nameof(userService));
            _exceptionHandler = exceptionHandler ?? throw new ArgumentNullException(nameof(exceptionHandler));
            
            WindowTitle = isNewReport ? "Report Defective Item" : "Edit Defective Item Report";
            
            // Initialize collections
            AvailableItems = new ObservableCollection<ItemViewModel>();
            Locations = new ObservableCollection<Location>();
            DefectTypes = new ObservableCollection<string>(new[] { 
                "Manufacturing Defect", 
                "Shipping Damage", 
                "Storage Damage", 
                "Customer Return", 
                "Expired", 
                "Other" 
            });
            SeverityLevels = new ObservableCollection<string>(new[] { 
                "Minor", 
                "Moderate", 
                "Major", 
                "Critical" 
            });
            StatusOptions = new ObservableCollection<string>(new[] { 
                "Reported", 
                "Under Investigation", 
                "Pending Supplier Response", 
                "Approved for Return", 
                "Rejected", 
                "Resolved" 
            });
            ActionOptions = new ObservableCollection<string>(new[] { 
                "Return to Supplier", 
                "Dispose", 
                "Repair", 
                "Sell at Discount", 
                "Use for Parts", 
                "Other" 
            });
            
            // Initialize commands
            BrowseCommand = new RelayCommand(_ => BrowsePhoto());
            SaveCommand = new RelayCommand(_ => Save(), _ => CanSave());
            CancelCommand = new RelayCommand(_ => Cancel());
            
            // Set default values for new reports
            if (isNewReport)
            {
                ReportDate = DateTime.Now;
                SelectedDefectType = "Manufacturing Defect";
                SelectedSeverity = "Moderate";
                SelectedStatus = "Reported";
                SelectedAction = "Return to Supplier";
                _defectiveItem.ReportedByUserId = _currentUser.Id;
                _defectiveItem.ReportedByUserName = _currentUser.FullName;
                _defectiveItem.ReportDate = DateTime.Now;
                
                // Default quantity to 1
                Quantity = 1;
            }
            else
            {
                // Load existing defective item values
                LoadExistingDefectiveItem();
            }
            
            // Load data
            LoadDataAsync();
        }
        
        #region Properties
        
        public event PropertyChangedEventHandler PropertyChanged;
        
        protected override void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
        
        public string WindowTitle
        {
            get => _windowTitle;
            set => SetProperty(ref _windowTitle, value);
        }
        
        public ObservableCollection<ItemViewModel> AvailableItems
        {
            get => _availableItems;
            set => SetProperty(ref _availableItems, value);
        }
        
        public ItemViewModel SelectedItem
        {
            get => _selectedItem;
            set
            {
                if (SetProperty(ref _selectedItem, value) && value != null)
                {
                    _defectiveItem.ItemId = value.Id;
                    _defectiveItem.ItemName = value.Name;
                    
                    // Update SKU and barcode
                    SKU = value.SKU;
                    Barcode = value.Barcode;
                    
                    // Update financial values
                    UpdateFinancialValues();
                    
                    // Update available quantity
                    UpdateAvailableQuantity();
                }
            }
        }
        
        public string SKU
        {
            get => _sku;
            set => SetProperty(ref _sku, value);
        }
        
        public string Barcode
        {
            get => _barcode;
            set => SetProperty(ref _barcode, value);
        }
        
        public ObservableCollection<Location> Locations
        {
            get => _locations;
            set => SetProperty(ref _locations, value);
        }
        
        public Location SelectedLocation
        {
            get => _selectedLocation;
            set
            {
                if (SetProperty(ref _selectedLocation, value) && value != null)
                {
                    _defectiveItem.LocationId = value.Id;
                    _defectiveItem.LocationName = value.Name;
                    
                    // Update available quantity based on selected item and location
                    UpdateAvailableQuantity();
                }
            }
        }
        
        public decimal Quantity
        {
            get => _quantity;
            set
            {
                if (SetProperty(ref _quantity, value))
                {
                    _defectiveItem.Quantity = value;
                    
                    // Update financial values
                    UpdateFinancialValues();
                }
            }
        }
        
        public decimal AvailableQuantity
        {
            get => _availableQuantity;
            set => SetProperty(ref _availableQuantity, value);
        }
        
        public string QuantityUnit
        {
            get => _quantityUnit;
            set => SetProperty(ref _quantityUnit, value);
        }
        
        public DateTime ReportDate
        {
            get => _reportDate;
            set
            {
                if (SetProperty(ref _reportDate, value))
                {
                    _defectiveItem.ReportDate = value;
                }
            }
        }
        
        public ObservableCollection<string> DefectTypes
        {
            get => _defectTypes;
            set => SetProperty(ref _defectTypes, value);
        }
        
        public string SelectedDefectType
        {
            get => _selectedDefectType;
            set
            {
                if (SetProperty(ref _selectedDefectType, value))
                {
                    _defectiveItem.DefectType = value;
                }
            }
        }
        
        public ObservableCollection<string> SeverityLevels
        {
            get => _severityLevels;
            set => SetProperty(ref _severityLevels, value);
        }
        
        public string SelectedSeverity
        {
            get => _selectedSeverity;
            set
            {
                if (SetProperty(ref _selectedSeverity, value))
                {
                    _defectiveItem.Severity = value;
                }
            }
        }
        
        public ObservableCollection<string> StatusOptions
        {
            get => _statusOptions;
            set => SetProperty(ref _statusOptions, value);
        }
        
        public string SelectedStatus
        {
            get => _selectedStatus;
            set
            {
                if (SetProperty(ref _selectedStatus, value))
                {
                    _defectiveItem.Status = value;
                }
            }
        }
        
        public string Description
        {
            get => _description;
            set
            {
                if (SetProperty(ref _description, value))
                {
                    _defectiveItem.Description = value;
                }
            }
        }
        
        public ObservableCollection<string> ActionOptions
        {
            get => _actionOptions;
            set => SetProperty(ref _actionOptions, value);
        }
        
        public string SelectedAction
        {
            get => _selectedAction;
            set
            {
                if (SetProperty(ref _selectedAction, value))
                {
                    _defectiveItem.ActionRequired = value;
                }
            }
        }
        
        public string PhotoPath
        {
            get => _photoPath;
            set => SetProperty(ref _photoPath, value);
        }
        
        public bool SendNotification
        {
            get => _sendNotification;
            set => SetProperty(ref _sendNotification, value);
        }
        
        public decimal CostValue
        {
            get => _costValue;
            set => SetProperty(ref _costValue, value);
        }
        
        public decimal RetailValue
        {
            get => _retailValue;
            set => SetProperty(ref _retailValue, value);
        }
        
        public bool IsBusy
        {
            get => _isBusy;
            set => SetProperty(ref _isBusy, value);
        }
        
        public bool IsNewReport => _isNewReport;
        
        #endregion
        
        #region Commands
        
        public ICommand BrowseCommand { get; }
        public ICommand SaveCommand { get; }
        public ICommand CancelCommand { get; }
        
        #endregion
        
        #region Methods
        
        private async void LoadDataAsync()
        {
            try
            {
                IsBusy = true;
                
                // Load items
                await LoadItemsAsync();
                
                // Load locations
                await LoadLocationsAsync();
                
                // If editing an existing defective item, select the appropriate item and location
                if (!_isNewReport && _defectiveItem.Id > 0)
                {
                    SelectedItem = AvailableItems.FirstOrDefault(i => i.Id == _defectiveItem.ItemId);
                    SelectedLocation = Locations.FirstOrDefault(l => l.Id == _defectiveItem.LocationId);
                }
                else
                {
                    // For new reports, select the first item and location by default if available
                    if (AvailableItems.Count > 0)
                    {
                        SelectedItem = AvailableItems.FirstOrDefault();
                    }
                    
                    if (Locations.Count > 0)
                    {
                        SelectedLocation = Locations.FirstOrDefault();
                    }
                }
            }
            catch (Exception ex)
            {
                await _exceptionHandler.HandleExceptionAsync(ex, "Error loading defective item data");
            }
            finally
            {
                IsBusy = false;
            }
        }
        
        private async Task LoadItemsAsync()
        {
            try
            {
                // Get all active items
                var items = await _dbContext.Items
                    .Where(i => !i.IsDeleted && i.IsActive)
                    .OrderBy(i => i.Name)
                    .ToListAsync();
                
                // Update available items collection
                AvailableItems.Clear();
                foreach (var item in items)
                {
                    var itemViewModel = new ItemViewModel
                    {
                        Id = item.Id,
                        Name = item.Name,
                        SKU = item.SKU,
                        Barcode = item.Barcode,
                        Category = item.Category,
                        CostPrice = item.CostPrice,
                        RetailPrice = item.RetailPrice
                    };
                    
                    AvailableItems.Add(itemViewModel);
                }
            }
            catch (Exception ex)
            {
                await _exceptionHandler.HandleExceptionAsync(ex, "Error loading items");
            }
        }
        
        private async Task LoadLocationsAsync()
        {
            try
            {
                // Get all active locations
                var locations = await _dbContext.Locations
                    .Where(l => l.IsActive)
                    .OrderBy(l => l.Name)
                    .ToListAsync();
                
                // Update locations collection
                Locations.Clear();
                foreach (var location in locations)
                {
                    Locations.Add(location);
                }
            }
            catch (Exception ex)
            {
                await _exceptionHandler.HandleExceptionAsync(ex, "Error loading locations");
            }
        }
        
        private void LoadExistingDefectiveItem()
        {
            // Set properties from existing defective item
            ReportDate = _defectiveItem.ReportDate;
            SelectedDefectType = _defectiveItem.DefectType;
            SelectedSeverity = _defectiveItem.Severity;
            SelectedStatus = _defectiveItem.Status;
            Description = _defectiveItem.Description;
            SelectedAction = _defectiveItem.ActionRequired;
            Quantity = _defectiveItem.Quantity;
            PhotoPath = _defectiveItem.PhotoPath;
        }
        
        private async void UpdateAvailableQuantity()
        {
            if (SelectedItem == null || SelectedLocation == null)
                return;
            
            try
            {
                // Get available quantity from stock
                var stock = await _dbContext.ItemStocks
                    .FirstOrDefaultAsync(s => s.ItemId == SelectedItem.Id && s.LocationId == SelectedLocation.Id);
                
                AvailableQuantity = stock?.Quantity ?? 0;
                
                // Ensure quantity doesn't exceed available
                if (Quantity > AvailableQuantity && IsNewReport)
                {
                    Quantity = AvailableQuantity;
                }
            }
            catch (Exception ex)
            {
                await _exceptionHandler.HandleExceptionAsync(ex, "Error updating available quantity");
            }
        }
        
        private void UpdateFinancialValues()
        {
            if (SelectedItem == null)
                return;
            
            // Calculate financial impact
            CostValue = SelectedItem.CostPrice * Quantity;
            RetailValue = SelectedItem.RetailPrice * Quantity;
            
            // Update defective item financial values
            _defectiveItem.CostValue = CostValue;
            _defectiveItem.RetailValue = RetailValue;
        }
        
        private void BrowsePhoto()
        {
            var openFileDialog = new OpenFileDialog
            {
                Filter = "Image Files (*.jpg;*.jpeg;*.png;*.gif)|*.jpg;*.jpeg;*.png;*.gif|All Files (*.*)|*.*",
                Title = "Select Photo"
            };
            
            if (openFileDialog.ShowDialog() == true)
            {
                PhotoPath = openFileDialog.FileName;
                _defectiveItem.PhotoPath = PhotoPath;
            }
        }
        
        private bool CanSave()
        {
            // Basic validation
            if (SelectedItem == null || SelectedLocation == null)
                return false;
            
            if (Quantity <= 0)
                return false;
            
            if (string.IsNullOrWhiteSpace(Description))
                return false;
            
            if (string.IsNullOrWhiteSpace(SelectedDefectType) || 
                string.IsNullOrWhiteSpace(SelectedSeverity) || 
                string.IsNullOrWhiteSpace(SelectedStatus) ||
                string.IsNullOrWhiteSpace(SelectedAction))
                return false;
            
            return true;
        }
        
        public void Cancel()
        {
            // Set dialog result
            DialogResult = false;
        }
        
        public bool? DialogResult { get; set; }
        
        public async void Save()
        {
            try
            {
                // Validate
                if (!CanSave())
                    return;
                
                IsBusy = true;
                
                // Ensure all properties are set
                _defectiveItem.ItemId = SelectedItem.Id;
                _defectiveItem.ItemName = SelectedItem.Name;
                _defectiveItem.SKU = SKU;
                _defectiveItem.Barcode = Barcode;
                _defectiveItem.LocationId = SelectedLocation.Id;
                _defectiveItem.LocationName = SelectedLocation.Name;
                _defectiveItem.Quantity = Quantity;
                _defectiveItem.ReportDate = ReportDate;
                _defectiveItem.DefectType = SelectedDefectType;
                _defectiveItem.Severity = SelectedSeverity;
                _defectiveItem.Status = SelectedStatus;
                _defectiveItem.Description = Description;
                _defectiveItem.ActionRequired = SelectedAction;
                _defectiveItem.PhotoPath = PhotoPath;
                
                // Set audit fields for new reports
                if (_isNewReport)
                {
                    _defectiveItem.ReportedByUserId = _currentUser.Id;
                    _defectiveItem.ReportedByUserName = _currentUser.FullName;
                    _defectiveItem.LastModifiedDate = DateTime.Now;
                    _defectiveItem.LastModifiedByUserId = _currentUser.Id;
                    _defectiveItem.LastModifiedByUserName = _currentUser.FullName;
                }
                else
                {
                    // Update audit fields
                    _defectiveItem.LastModifiedDate = DateTime.Now;
                    _defectiveItem.LastModifiedByUserId = _currentUser.Id;
                    _defectiveItem.LastModifiedByUserName = _currentUser.FullName;
                }
                
                // Save to database
                if (_isNewReport)
                {
                    await _dbContext.DefectiveItems.AddAsync(_defectiveItem);
                }
                else
                {
                    _dbContext.DefectiveItems.Update(_defectiveItem);
                }
                
                await _dbContext.SaveChangesAsync();
                
                // Log audit trail
                string action = _isNewReport ? "Create" : "Update";
                await _auditService.LogActionAsync(
                    "DefectiveItem",
                    action,
                    $"{action}d defective item report for {Quantity} {SelectedItem.Name} at {SelectedLocation.Name}",
                    _defectiveItem.Id.ToString());
                
                // Send notification if requested
                if (SendNotification)
                {
                    await SendDefectiveItemNotificationAsync();
                }
                
                // Set dialog result
                DialogResult = true;
            }
            catch (Exception ex)
            {
                await _exceptionHandler.HandleExceptionAsync(ex, "Error saving defective item report");
                DialogResult = false;
            }
            finally
            {
                IsBusy = false;
            }
        }
        
        private async Task SendDefectiveItemNotificationAsync()
        {
            try
            {
                // Get managers
                var managers = await _dbContext.Users
                    .Where(u => u.Role == UserRole.Admin || 
                               (u.Role == UserRole.BasementManager && u.PrimaryLocationId == SelectedLocation.Id))
                    .Where(u => u.IsActive)
                    .ToListAsync();
                
                if (managers.Any())
                {
                    // Create notification for each manager
                    foreach (var manager in managers)
                    {
                        var notification = new Notification
                        {
                            Title = "Defective Item Report",
                            Message = $"New defective item report for {Quantity} {SelectedItem.Name} at {SelectedLocation.Name}. Severity: {SelectedSeverity}. Action: {SelectedAction}",
                            Type = NotificationType.DefectiveItem,
                            UserId = manager.Id,
                            SourceUserId = _currentUser.Id,
                            CreatedDate = DateTime.Now,
                            Priority = SelectedSeverity == "Critical" ? NotificationPriority.High : NotificationPriority.Normal,
                            IsRead = false,
                            RelatedEntityId = _defectiveItem.Id.ToString()
                        };
                        
                        await _notificationService.SendNotificationAsync(notification);
                    }
                }
            }
            catch (Exception ex)
            {
                await _exceptionHandler.HandleExceptionAsync(ex, "Error sending defective item notification");
            }
        }
        
        /// <summary>
        /// Returns the defective item object with all updated values
        /// </summary>
        /// <returns>The updated defective item</returns>
        public DefectiveItem GetDefectiveItem()
        {
            return _defectiveItem;
        }
        
        #endregion
    }
}
