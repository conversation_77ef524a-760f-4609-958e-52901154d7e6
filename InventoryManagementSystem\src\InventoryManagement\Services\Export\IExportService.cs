namespace InventoryManagement.Services.Export
{
    /// <summary>
    /// Interface for export services
    /// </summary>
    public interface IExportService
    {
        /// <summary>
        /// Export data to PDF format
        /// </summary>
        /// <param name="data">Data to export</param>
        /// <returns>PDF file as byte array</returns>
        Task<byte[]> ExportToPdfAsync(object data);

        /// <summary>
        /// Export data to Excel format
        /// </summary>
        /// <param name="data">Data to export</param>
        /// <returns>Excel file as byte array</returns>
        Task<byte[]> ExportToExcelAsync(object data);

        /// <summary>
        /// Export data to CSV format
        /// </summary>
        /// <param name="data">Data to export</param>
        /// <returns>CSV file as byte array</returns>
        Task<byte[]> ExportToCsvAsync(object data);

        /// <summary>
        /// Export data to JSON format
        /// </summary>
        /// <param name="data">Data to export</param>
        /// <returns>JSON file as byte array</returns>
        Task<byte[]> ExportToJsonAsync(object data);
    }
}
