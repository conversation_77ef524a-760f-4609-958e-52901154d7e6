using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using InventoryManagement.Models;
using InventoryManagement.Infrastructure.Authentication;

namespace InventoryManagement.Services
{
    /// <summary>
    /// Interface for offline authentication service
    /// </summary>
    public interface IOfflineAuthService
    {
        /// <summary>
        /// Authenticate a user with username and password
        /// </summary>
        /// <param name="username">Username</param>
        /// <param name="password">Password</param>
        /// <returns>Authentication result with user if successful</returns>
        Task<AuthResult> AuthenticateAsync(string username, string password);
        
        /// <summary>
        /// Authenticate a user with token
        /// </summary>
        /// <param name="token">Authentication token</param>
        /// <returns>Authentication result with user if successful</returns>
        Task<AuthResult> AuthenticateWithTokenAsync(string token);
        
        /// <summary>
        /// Create a new session for a user
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <returns>Session token</returns>
        Task<string> CreateSessionAsync(int userId);
        
        /// <summary>
        /// Validate a user session
        /// </summary>
        /// <param name="token">Session token</param>
        /// <returns>User if session is valid</returns>
        Task<User> ValidateSessionAsync(string token);
        
        /// <summary>
        /// End the current user session
        /// </summary>
        /// <param name="token">Session token</param>
        /// <returns>True if successful</returns>
        Task<bool> EndSessionAsync(string token);
        
        /// <summary>
        /// Get current authenticated user
        /// </summary>
        /// <returns>Current user or null if not authenticated</returns>
        Task<User> GetCurrentUserAsync();
        
        /// <summary>
        /// Set current authenticated user
        /// </summary>
        /// <param name="user">User to set as current</param>
        void SetCurrentUser(User user);
    }
    

}
