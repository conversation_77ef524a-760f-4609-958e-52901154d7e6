C:\Users\<USER>\Desktop\tom general trading\InventoryManagementSystem\src\InventoryManagement\obj\Debug\net8.0-windows\InventoryManagement.csproj.AssemblyReference.cache
C:\Users\<USER>\Desktop\tom general trading\InventoryManagementSystem\src\InventoryManagement\obj\Debug\net8.0-windows\Views\Dialogs\InventoryAdjustmentDialog.baml
C:\Users\<USER>\Desktop\tom general trading\InventoryManagementSystem\src\InventoryManagement\obj\Debug\net8.0-windows\Views\Dialogs\StorageInfoDialog.baml
C:\Users\<USER>\Desktop\tom general trading\InventoryManagementSystem\src\InventoryManagement\obj\Debug\net8.0-windows\Dialogs\CategorySelectionDialog.baml
C:\Users\<USER>\Desktop\tom general trading\InventoryManagementSystem\src\InventoryManagement\obj\Debug\net8.0-windows\Dialogs\InputDialog.baml
C:\Users\<USER>\Desktop\tom general trading\InventoryManagementSystem\src\InventoryManagement\obj\Debug\net8.0-windows\Dialogs\QuickEditItemDialog.baml
C:\Users\<USER>\Desktop\tom general trading\InventoryManagementSystem\src\InventoryManagement\obj\Debug\net8.0-windows\Resources\IconResources.baml
C:\Users\<USER>\Desktop\tom general trading\InventoryManagementSystem\src\InventoryManagement\obj\Debug\net8.0-windows\Views\AdminDashboard.g.cs
C:\Users\<USER>\Desktop\tom general trading\InventoryManagementSystem\src\InventoryManagement\obj\Debug\net8.0-windows\Views\AdminDashboardDirect.g.cs
C:\Users\<USER>\Desktop\tom general trading\InventoryManagementSystem\src\InventoryManagement\obj\Debug\net8.0-windows\Views\AdminDashboardView.g.cs
C:\Users\<USER>\Desktop\tom general trading\InventoryManagementSystem\src\InventoryManagement\obj\Debug\net8.0-windows\Views\BarcodeDashboardView.g.cs
C:\Users\<USER>\Desktop\tom general trading\InventoryManagementSystem\src\InventoryManagement\obj\Debug\net8.0-windows\Views\BarcodeScannerView.g.cs
C:\Users\<USER>\Desktop\tom general trading\InventoryManagementSystem\src\InventoryManagement\obj\Debug\net8.0-windows\Views\BasementManagerDashboard.g.cs
C:\Users\<USER>\Desktop\tom general trading\InventoryManagementSystem\src\InventoryManagement\obj\Debug\net8.0-windows\Views\BasementManagerDashboardView.g.cs
C:\Users\<USER>\Desktop\tom general trading\InventoryManagementSystem\src\InventoryManagement\obj\Debug\net8.0-windows\Views\CashierDashboard.g.cs
C:\Users\<USER>\Desktop\tom general trading\InventoryManagementSystem\src\InventoryManagement\obj\Debug\net8.0-windows\Views\CashierDashboardView.g.cs
C:\Users\<USER>\Desktop\tom general trading\InventoryManagementSystem\src\InventoryManagement\obj\Debug\net8.0-windows\Views\ConflictResolutionView.g.cs
C:\Users\<USER>\Desktop\tom general trading\InventoryManagementSystem\src\InventoryManagement\obj\Debug\net8.0-windows\Views\DatabaseSetupGuide.g.cs
C:\Users\<USER>\Desktop\tom general trading\InventoryManagementSystem\src\InventoryManagement\obj\Debug\net8.0-windows\Views\DataIntegrity\DataIntegrityView.g.cs
C:\Users\<USER>\Desktop\tom general trading\InventoryManagementSystem\src\InventoryManagement\obj\Debug\net8.0-windows\Views\DefectiveItemView.g.cs
C:\Users\<USER>\Desktop\tom general trading\InventoryManagementSystem\src\InventoryManagement\obj\Debug\net8.0-windows\Views\Dialogs\CategoryDialog.g.cs
C:\Users\<USER>\Desktop\tom general trading\InventoryManagementSystem\src\InventoryManagement\obj\Debug\net8.0-windows\Views\Dialogs\ConflictResolutionDialog.g.cs
C:\Users\<USER>\Desktop\tom general trading\InventoryManagementSystem\src\InventoryManagement\obj\Debug\net8.0-windows\Views\Dialogs\DefectiveItemDialog.g.cs
C:\Users\<USER>\Desktop\tom general trading\InventoryManagementSystem\src\InventoryManagement\obj\Debug\net8.0-windows\Views\Dialogs\ExchangeDialog.g.cs
C:\Users\<USER>\Desktop\tom general trading\InventoryManagementSystem\src\InventoryManagement\obj\Debug\net8.0-windows\Views\Dialogs\HelpViewerDialog.g.cs
C:\Users\<USER>\Desktop\tom general trading\InventoryManagementSystem\src\InventoryManagement\obj\Debug\net8.0-windows\Views\Dialogs\InventoryAdjustmentDialog.g.cs
C:\Users\<USER>\Desktop\tom general trading\InventoryManagementSystem\src\InventoryManagement\obj\Debug\net8.0-windows\Views\Dialogs\ProductDialog.g.cs
C:\Users\<USER>\Desktop\tom general trading\InventoryManagementSystem\src\InventoryManagement\obj\Debug\net8.0-windows\Views\Dialogs\StorageInfoDialog.g.cs
C:\Users\<USER>\Desktop\tom general trading\InventoryManagementSystem\src\InventoryManagement\obj\Debug\net8.0-windows\Views\Dialogs\SyncProgressDialog.g.cs
C:\Users\<USER>\Desktop\tom general trading\InventoryManagementSystem\src\InventoryManagement\obj\Debug\net8.0-windows\Views\Dialogs\TransferDialog.g.cs
C:\Users\<USER>\Desktop\tom general trading\InventoryManagementSystem\src\InventoryManagement\obj\Debug\net8.0-windows\Views\ExcelDataImportView.g.cs
C:\Users\<USER>\Desktop\tom general trading\InventoryManagementSystem\src\InventoryManagement\obj\Debug\net8.0-windows\Views\InventoryDashboardView.g.cs
C:\Users\<USER>\Desktop\tom general trading\InventoryManagementSystem\src\InventoryManagement\obj\Debug\net8.0-windows\Views\InventoryReconciliationView.g.cs
C:\Users\<USER>\Desktop\tom general trading\InventoryManagementSystem\src\InventoryManagement\obj\Debug\net8.0-windows\Views\InventoryTransferView.g.cs
C:\Users\<USER>\Desktop\tom general trading\InventoryManagementSystem\src\InventoryManagement\obj\Debug\net8.0-windows\Views\ItemExchangeView.g.cs
C:\Users\<USER>\Desktop\tom general trading\InventoryManagementSystem\src\InventoryManagement\obj\Debug\net8.0-windows\Views\LocationItemsView.g.cs
C:\Users\<USER>\Desktop\tom general trading\InventoryManagementSystem\src\InventoryManagement\obj\Debug\net8.0-windows\Views\LoginView.g.cs
C:\Users\<USER>\Desktop\tom general trading\InventoryManagementSystem\src\InventoryManagement\obj\Debug\net8.0-windows\Views\LoginWindow.g.cs
C:\Users\<USER>\Desktop\tom general trading\InventoryManagementSystem\src\InventoryManagement\obj\Debug\net8.0-windows\Views\MainDashboard.g.cs
C:\Users\<USER>\Desktop\tom general trading\InventoryManagementSystem\src\InventoryManagement\obj\Debug\net8.0-windows\Views\PointOfSaleView.g.cs
C:\Users\<USER>\Desktop\tom general trading\InventoryManagementSystem\src\InventoryManagement\obj\Debug\net8.0-windows\Views\Settings\SynchronizationSettingsView.g.cs
C:\Users\<USER>\Desktop\tom general trading\InventoryManagementSystem\src\InventoryManagement\obj\Debug\net8.0-windows\Views\SyncHistoryView.g.cs
C:\Users\<USER>\Desktop\tom general trading\InventoryManagementSystem\src\InventoryManagement\obj\Debug\net8.0-windows\Views\SyncProgressView.g.cs
C:\Users\<USER>\Desktop\tom general trading\InventoryManagementSystem\src\InventoryManagement\obj\Debug\net8.0-windows\Views\TransactionVerificationView.g.cs
C:\Users\<USER>\Desktop\tom general trading\InventoryManagementSystem\src\InventoryManagement\obj\Debug\net8.0-windows\Views\UnhandledExceptionDialog.g.cs
C:\Users\<USER>\Desktop\tom general trading\InventoryManagementSystem\src\InventoryManagement\obj\Debug\net8.0-windows\Controls\BatchTrackingPanel.g.cs
C:\Users\<USER>\Desktop\tom general trading\InventoryManagementSystem\src\InventoryManagement\obj\Debug\net8.0-windows\Controls\ConnectionStatusIndicator.g.cs
C:\Users\<USER>\Desktop\tom general trading\InventoryManagementSystem\src\InventoryManagement\obj\Debug\net8.0-windows\Controls\CountModeControl.g.cs
C:\Users\<USER>\Desktop\tom general trading\InventoryManagementSystem\src\InventoryManagement\obj\Debug\net8.0-windows\Controls\DiscrepancyResolutionControl.g.cs
C:\Users\<USER>\Desktop\tom general trading\InventoryManagementSystem\src\InventoryManagement\obj\Debug\net8.0-windows\Controls\HelpSystem\HelpTooltip.g.cs
C:\Users\<USER>\Desktop\tom general trading\InventoryManagementSystem\src\InventoryManagement\obj\Debug\net8.0-windows\Controls\LanguageSelector.g.cs
C:\Users\<USER>\Desktop\tom general trading\InventoryManagementSystem\src\InventoryManagement\obj\Debug\net8.0-windows\Controls\NotificationControl.g.cs
C:\Users\<USER>\Desktop\tom general trading\InventoryManagementSystem\src\InventoryManagement\obj\Debug\net8.0-windows\Controls\ReconciliationListControl.g.cs
C:\Users\<USER>\Desktop\tom general trading\InventoryManagementSystem\src\InventoryManagement\obj\Debug\net8.0-windows\Controls\ReconciliationReportControl.g.cs
C:\Users\<USER>\Desktop\tom general trading\InventoryManagementSystem\src\InventoryManagement\obj\Debug\net8.0-windows\Dialogs\CategorySelectionDialog.g.cs
C:\Users\<USER>\Desktop\tom general trading\InventoryManagementSystem\src\InventoryManagement\obj\Debug\net8.0-windows\Dialogs\InputDialog.g.cs
C:\Users\<USER>\Desktop\tom general trading\InventoryManagementSystem\src\InventoryManagement\obj\Debug\net8.0-windows\Dialogs\QuickEditItemDialog.g.cs
C:\Users\<USER>\Desktop\tom general trading\InventoryManagementSystem\src\InventoryManagement\obj\Debug\net8.0-windows\App.g.cs
C:\Users\<USER>\Desktop\tom general trading\InventoryManagementSystem\src\InventoryManagement\obj\Debug\net8.0-windows\GeneratedInternalTypeHelper.g.cs
C:\Users\<USER>\Desktop\tom general trading\InventoryManagementSystem\src\InventoryManagement\obj\Debug\net8.0-windows\InventoryManagement_MarkupCompile.cache
C:\Users\<USER>\Desktop\tom general trading\InventoryManagementSystem\src\InventoryManagement\obj\Debug\net8.0-windows\InventoryManagement_MarkupCompile.lref
C:\Users\<USER>\Desktop\tom general trading\InventoryManagementSystem\src\InventoryManagement\obj\Debug\net8.0-windows\Views\DatabaseSetupProgressWindow.baml
C:\Users\<USER>\Desktop\tom general trading\InventoryManagementSystem\src\InventoryManagement\obj\Debug\net8.0-windows\Views\DatabaseSetupProgressWindow.g.cs
C:\Users\<USER>\Desktop\tom general trading\InventoryManagementSystem\src\InventoryManagement\obj\Debug\net8.0-windows\Views\CustomerManagementView.baml
C:\Users\<USER>\Desktop\tom general trading\InventoryManagementSystem\src\InventoryManagement\obj\Debug\net8.0-windows\Views\Reports\ReportsDashboardView.baml
C:\Users\<USER>\Desktop\tom general trading\InventoryManagementSystem\src\InventoryManagement\obj\Debug\net8.0-windows\Views\Settings\HardwareSettingsView.baml
C:\Users\<USER>\Desktop\tom general trading\InventoryManagementSystem\src\InventoryManagement\obj\Debug\net8.0-windows\Views\CustomerManagementView.g.cs
C:\Users\<USER>\Desktop\tom general trading\InventoryManagementSystem\src\InventoryManagement\obj\Debug\net8.0-windows\Views\Reports\ReportsDashboardView.g.cs
C:\Users\<USER>\Desktop\tom general trading\InventoryManagementSystem\src\InventoryManagement\obj\Debug\net8.0-windows\Views\Settings\HardwareSettingsView.g.cs
